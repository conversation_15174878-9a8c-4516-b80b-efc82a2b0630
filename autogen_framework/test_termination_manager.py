#!/usr/bin/env python3
"""
终止条件管理器测试

测试TerminationManager的核心功能：
1. 单个终止条件的创建
2. 组合终止条件的创建
3. 从配置创建终止条件
4. 终止条件信息查询
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from managers.termination_manager import TerminationManager, get_termination_manager


def test_termination_manager_basic_functionality():
    """测试终止条件管理器基本功能"""
    print("=== 终止条件管理器基本功能测试 ===")
    
    try:
        # 获取终止条件管理器实例
        termination_manager = get_termination_manager()
        print(f"✓ 终止条件管理器初始化成功")
        
        # 列出可用的终止条件
        available_conditions = termination_manager.get_available_conditions()
        print(f"✓ 可用终止条件: {available_conditions}")
        
        # 获取终止条件信息
        for condition_type in available_conditions[:3]:  # 只测试前3个
            try:
                info = termination_manager.get_condition_info(condition_type)
                print(f"✓ 终止条件 '{condition_type}' 信息:")
                print(f"  - 描述: {info['description']}")
                print(f"  - 参数: {info['parameters']}")
            except Exception as e:
                print(f"✗ 获取终止条件 '{condition_type}' 信息失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 终止条件管理器基本功能测试失败: {e}")
        return False


def test_single_termination_creation():
    """测试单个终止条件创建"""
    print("\n=== 单个终止条件创建测试 ===")
    
    try:
        termination_manager = get_termination_manager()
        
        # 测试创建最大消息数终止条件
        try:
            max_msg_condition = termination_manager.create_termination("max_message", max_messages=10)
            print(f"✓ 最大消息数终止条件创建成功: {type(max_msg_condition)}")
        except Exception as e:
            print(f"✗ 最大消息数终止条件创建失败: {e}")
        
        # 测试创建文本提及终止条件
        try:
            text_condition = termination_manager.create_termination("text_mention", text="TERMINATE")
            print(f"✓ 文本提及终止条件创建成功: {type(text_condition)}")
        except Exception as e:
            print(f"✗ 文本提及终止条件创建失败: {e}")
        
        # 测试创建超时终止条件
        try:
            timeout_condition = termination_manager.create_termination("timeout", timeout_seconds=30.0)
            print(f"✓ 超时终止条件创建成功: {type(timeout_condition)}")
        except Exception as e:
            print(f"✗ 超时终止条件创建失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 单个终止条件创建测试失败: {e}")
        return False


def test_combined_termination_creation():
    """测试组合终止条件创建"""
    print("\n=== 组合终止条件创建测试 ===")
    
    try:
        termination_manager = get_termination_manager()
        
        # 测试OR组合
        try:
            or_conditions = [
                {"type": "max_message", "max_messages": 10},
                {"type": "text_mention", "text": "APPROVE"}
            ]
            or_combined = termination_manager.create_combined_termination(or_conditions, "or")
            print(f"✓ OR组合终止条件创建成功: {type(or_combined)}")
        except Exception as e:
            print(f"✗ OR组合终止条件创建失败: {e}")
        
        # 测试AND组合
        try:
            and_conditions = [
                {"type": "max_message", "max_messages": 20},
                {"type": "timeout", "timeout_seconds": 60.0}
            ]
            and_combined = termination_manager.create_combined_termination(and_conditions, "and")
            print(f"✓ AND组合终止条件创建成功: {type(and_combined)}")
        except Exception as e:
            print(f"✗ AND组合终止条件创建失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 组合终止条件创建测试失败: {e}")
        return False


def test_config_based_creation():
    """测试基于配置的终止条件创建"""
    print("\n=== 基于配置的终止条件创建测试 ===")
    
    try:
        termination_manager = get_termination_manager()
        
        # 测试单个条件配置
        try:
            single_config = {
                "type": "max_message",
                "max_messages": 15
            }
            single_condition = termination_manager.create_from_config(single_config)
            print(f"✓ 单个条件配置创建成功: {type(single_condition)}")
        except Exception as e:
            print(f"✗ 单个条件配置创建失败: {e}")
        
        # 测试组合条件配置
        try:
            combined_config = {
                "conditions": [
                    {"type": "max_message", "max_messages": 10},
                    {"type": "text_mention", "text": "DONE"},
                    {"type": "timeout", "timeout_seconds": 120.0}
                ],
                "operator": "or"
            }
            combined_condition = termination_manager.create_from_config(combined_config)
            print(f"✓ 组合条件配置创建成功: {type(combined_condition)}")
        except Exception as e:
            print(f"✗ 组合条件配置创建失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 基于配置的终止条件创建测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n=== 错误处理测试 ===")
    
    try:
        termination_manager = get_termination_manager()
        
        # 测试未知终止条件类型
        try:
            termination_manager.create_termination("unknown_type")
            print("✗ 应该抛出异常但没有")
            return False
        except ValueError as e:
            print(f"✓ 正确处理未知终止条件类型: {e}")
        
        # 测试无效操作符
        try:
            conditions = [{"type": "max_message", "max_messages": 5}]
            termination_manager.create_combined_termination(conditions, "invalid_op")
            print("✗ 应该抛出异常但没有")
            return False
        except ValueError as e:
            print(f"✓ 正确处理无效操作符: {e}")
        
        # 测试空条件列表
        try:
            termination_manager.create_combined_termination([], "or")
            print("✗ 应该抛出异常但没有")
            return False
        except ValueError as e:
            print(f"✓ 正确处理空条件列表: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 错误处理测试失败: {e}")
        return False


def test_singleton_behavior():
    """测试单例行为"""
    print("\n=== 单例行为测试 ===")
    
    try:
        manager1 = get_termination_manager()
        manager2 = get_termination_manager()
        
        # 验证是同一个实例
        if manager1 is manager2:
            print("✓ 单例行为正常")
            return True
        else:
            print("✗ 单例行为异常")
            return False
        
    except Exception as e:
        print(f"✗ 单例行为测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始终止条件管理器测试...")
    
    tests = [
        test_termination_manager_basic_functionality,
        test_single_termination_creation,
        test_combined_termination_creation,
        test_config_based_creation,
        test_error_handling,
        test_singleton_behavior,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print(f"✓ {test.__name__} 通过")
            else:
                print(f"✗ {test.__name__} 失败")
        except Exception as e:
            print(f"✗ {test.__name__} 异常: {e}")
    
    print(f"\n=== 测试总结 ===")
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！终止条件管理器功能正常。")
        return True
    else:
        print("❌ 部分测试失败，请检查终止条件管理器实现。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
