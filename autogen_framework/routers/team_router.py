"""
Team API路由

提供Team相关的REST API接口，包括执行、列表查询和信息获取。
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List
import logging

from services.team_service import TeamService
from infrastructure.dependencies import get_team_service
from infrastructure.exceptions import ServiceError, TeamError

try:
    from autogen_core import TRACE_LOGGER_NAME
    AUTOGEN_AVAILABLE = True
except ImportError:
    TRACE_LOGGER_NAME = "autogen_core.trace"
    AUTOGEN_AVAILABLE = False

# 使用AutoGen的trace logger
logger = logging.getLogger(f"{TRACE_LOGGER_NAME}.team_router")

# 创建路由器
router = APIRouter(prefix="/autogen", tags=["teams"])


# 请求和响应模型
class TeamRunRequest(BaseModel):
    """Team执行请求模型"""
    name: str = Field(..., description="Team名称/类型")
    message: str = Field(..., description="用户消息")
    model: str = Field(..., description="模型别名")
    options: Optional[Dict[str, Any]] = Field(None, description="其他执行选项")
    
    class Config:
        schema_extra = {
            "example": {
                "name": "development",
                "message": "请开发一个计算斐波那契数列的Python函数",
                "model": "Qwen2.5-32B-Instruct",
                "options": {
                    "timeout": 60,
                    "max_rounds": 5,
                    "stream": False
                }
            }
        }


class TeamRunResponse(BaseModel):
    """Team执行响应模型"""
    status: str = Field(..., description="执行状态")
    result: Any = Field(..., description="执行结果")
    metadata: Dict[str, Any] = Field(..., description="执行元数据")
    
    class Config:
        schema_extra = {
            "example": {
                "status": "success",
                "result": "开发团队协作完成了斐波那契函数的实现和测试。",
                "metadata": {
                    "team_name": "development",
                    "model": "Qwen2.5-32B-Instruct",
                    "execution_time": 15.3,
                    "rounds": 3,
                    "agents_involved": ["developer", "code_reviewer"]
                }
            }
        }


class TeamListResponse(BaseModel):
    """Team列表响应模型"""
    teams: List[str] = Field(..., description="可用Team列表")
    total: int = Field(..., description="Team总数")
    metadata: Dict[str, Any] = Field(..., description="列表元数据")


class TeamInfoResponse(BaseModel):
    """Team信息响应模型"""
    name: str = Field(..., description="Team名称")
    description: str = Field(..., description="Team描述")
    agents: List[str] = Field(..., description="Team中的Agent列表")
    capabilities: List[str] = Field(..., description="Team能力列表")
    metadata: Dict[str, Any] = Field(..., description="Team元数据")


# API端点
@router.post("/run/team", response_model=TeamRunResponse)
async def run_team(
    request: TeamRunRequest,
    service: TeamService = Depends(get_team_service)
):
    """
    执行Team任务
    
    执行指定的Team，多个Agent协作处理用户消息并返回结果。
    支持多种模型和执行选项。
    """
    try:
        logger.info(f"Running team: {request.name} with model: {request.model}")
        
        result = await service.run(
            name=request.name,
            message=request.message,
            model=request.model,
            **(request.options or {})
        )
        
        logger.info(f"Team run completed: {request.name}")
        return TeamRunResponse(**result)
    
    except (ServiceError, TeamError) as e:
        logger.error(f"Team service error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    
    except Exception as e:
        logger.error(f"Unexpected error in team run: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/teams", response_model=TeamListResponse)
async def list_teams(
    service: TeamService = Depends(get_team_service)
):
    """
    列出所有可用的Team
    
    返回系统中注册的所有Team类型及其基本信息。
    """
    try:
        logger.info("Listing available teams")
        
        result = await service.list_teams()
        
        # 提取team名称列表
        team_names = [team["name"] for team in result["teams"]]

        response = TeamListResponse(
            teams=team_names,
            total=result["count"],
            metadata={"timestamp": result["timestamp"], "teams_detail": result["teams"]}
        )
        
        logger.info(f"Listed {result['count']} teams")
        return response
    
    except ServiceError as e:
        logger.error(f"Team service error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    
    except Exception as e:
        logger.error(f"Unexpected error in list teams: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/teams/{name}", response_model=TeamInfoResponse)
async def get_team_info(
    name: str,
    service: TeamService = Depends(get_team_service)
):
    """
    获取指定Team的详细信息
    
    返回Team的描述、包含的Agent、能力和配置信息。
    """
    try:
        logger.info(f"Getting team info: {name}")
        
        result = await service.get_team_info(name)
        
        team_info = result["team_info"]
        response = TeamInfoResponse(
            name=team_info.get("name", name),
            description=team_info.get("description", ""),
            agents=team_info.get("agents", []),
            capabilities=team_info.get("capabilities", []),
            metadata={"timestamp": result["timestamp"]}
        )
        
        logger.info(f"Retrieved info for team: {name}")
        return response
    
    except ServiceError as e:
        logger.error(f"Team service error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    
    except Exception as e:
        logger.error(f"Unexpected error in get team info: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
