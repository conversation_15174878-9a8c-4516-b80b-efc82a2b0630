#!/usr/bin/env python3
"""
服务层测试

测试Agent和Team服务的基本功能。
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量，避免代理问题
os.environ['https_proxy'] = ''
os.environ['http_proxy'] = ''
os.environ['all_proxy'] = ''


async def test_agent_service():
    """测试Agent服务"""
    print("=== 测试Agent服务 ===")
    
    try:
        from services.agent_service import AgentService
        
        # 创建Agent服务
        agent_service = AgentService()
        print("✓ Agent服务创建成功")
        
        # 测试列出可用Agent
        agents_result = await agent_service.list_agents()
        print(f"✓ 列出Agent成功: {agents_result['count']} 个Agent")
        
        if agents_result['count'] > 0:
            # 获取第一个Agent的信息
            first_agent = agents_result['agents'][0]['name']
            agent_info = await agent_service.get_agent_info(first_agent)
            print(f"✓ 获取Agent信息成功: {first_agent}")
            
            # 测试运行Agent（使用模拟模式）
            try:
                result = await agent_service.run(
                    name=first_agent,
                    message="Hello, this is a test message",
                    model="nebulacoder-v6.0",
                    timeout=10
                )
                print(f"✓ Agent运行成功: {result['status']}")
                print(f"  执行时间: {result['metadata']['execution_time']:.2f}s")
            except Exception as e:
                print(f"⚠ Agent运行测试跳过（预期，因为模型配置）: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ Agent服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_team_service():
    """测试Team服务"""
    print("\n=== 测试Team服务 ===")
    
    try:
        from services.team_service import TeamService
        
        # 创建Team服务
        team_service = TeamService()
        print("✓ Team服务创建成功")
        
        # 测试列出可用Team
        teams_result = await team_service.list_teams()
        print(f"✓ 列出Team成功: {teams_result['count']} 个Team")
        
        if teams_result['count'] > 0:
            # 获取第一个Team的信息
            first_team = teams_result['teams'][0]['name']
            team_info = await team_service.get_team_info(first_team)
            print(f"✓ 获取Team信息成功: {first_team}")
            
            # 测试运行Team（使用模拟模式）
            try:
                result = await team_service.run(
                    name=first_team,
                    message="Hello team, this is a test message",
                    model="nebulacoder-v6.0",
                    timeout=15
                )
                print(f"✓ Team运行成功: {result['status']}")
                print(f"  执行时间: {result['metadata']['execution_time']:.2f}s")
            except Exception as e:
                print(f"⚠ Team运行测试跳过（预期，因为模型配置）: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ Team服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_service_integration():
    """测试服务层集成"""
    print("\n=== 测试服务层集成 ===")
    
    try:
        from services.agent_service import AgentService
        from services.team_service import TeamService
        from managers.agent_manager import get_agent_manager
        from managers.team_manager import get_team_manager
        
        # 获取管理器实例
        agent_manager = get_agent_manager()
        team_manager = get_team_manager()
        
        # 创建服务实例，共享管理器
        agent_service = AgentService(agent_manager)
        team_service = TeamService(team_manager, agent_manager)
        
        print("✓ 服务层集成创建成功")
        
        # 测试并发访问
        tasks = []
        tasks.append(agent_service.list_agents())
        tasks.append(team_service.list_teams())
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        success_count = sum(1 for r in results if not isinstance(r, Exception))
        print(f"✓ 并发测试完成: {success_count}/{len(tasks)} 成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 服务层集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    try:
        from services.agent_service import AgentService
        from services.team_service import TeamService
        from infrastructure.exceptions import ServiceError
        
        agent_service = AgentService()
        team_service = TeamService()
        
        # 测试无效参数
        test_cases = [
            # Agent服务错误测试
            (agent_service.run, {"name": "", "message": "test", "model": "test"}),
            (agent_service.run, {"name": "test", "message": "", "model": "test"}),
            (agent_service.run, {"name": "test", "message": "test", "model": ""}),
            (agent_service.run, {"name": "nonexistent", "message": "test", "model": "test"}),
            (agent_service.get_agent_info, {"name": "nonexistent"}),
            
            # Team服务错误测试
            (team_service.run, {"name": "", "message": "test", "model": "test"}),
            (team_service.run, {"name": "test", "message": "", "model": "test"}),
            (team_service.run, {"name": "test", "message": "test", "model": ""}),
            (team_service.run, {"name": "nonexistent", "message": "test", "model": "test"}),
            (team_service.get_team_info, {"name": "nonexistent"}),
        ]
        
        error_count = 0
        for func, kwargs in test_cases:
            try:
                await func(**kwargs)
                print(f"✗ 预期错误但成功: {func.__name__} with {kwargs}")
            except ServiceError:
                error_count += 1
            except Exception as e:
                print(f"✗ 意外错误类型: {func.__name__} - {type(e).__name__}: {e}")
        
        print(f"✓ 错误处理测试: {error_count}/{len(test_cases)} 个错误正确捕获")
        
        return True
        
    except Exception as e:
        print(f"✗ 错误处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("开始服务层测试...")
    print("=" * 50)
    
    tests = [
        test_agent_service,
        test_team_service,
        test_service_integration,
        test_error_handling
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"✗ 测试 {test.__name__} 异常: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("服务层测试总结:")
    
    success_count = sum(results)
    total_count = len(results)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✓" if result else "✗"
        print(f"{status} {test.__name__}")
    
    print(f"\n总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 所有服务层测试通过！")
        return True
    else:
        print("⚠ 部分测试失败，请检查上述错误信息")
        return False


if __name__ == "__main__":
    # 运行测试
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
