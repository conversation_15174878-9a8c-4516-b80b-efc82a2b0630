"""
API路由测试

测试Agent和Team的REST API接口功能。
"""

import asyncio
import json
from fastapi.testclient import TestClient
from main import app

# 创建测试客户端
client = TestClient(app)


def test_health_check():
    """测试健康检查接口"""
    print("🔍 Testing health check endpoint...")
    
    response = client.get("/health")
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "healthy"
    assert "app_name" in data
    assert "version" in data
    
    print("✅ Health check test passed")


def test_root_endpoint():
    """测试根接口"""
    print("🔍 Testing root endpoint...")
    
    response = client.get("/")
    assert response.status_code == 200
    
    data = response.json()
    assert "app_name" in data
    assert "version" in data
    assert "description" in data
    
    print("✅ Root endpoint test passed")


def test_list_agents():
    """测试列出Agent接口"""
    print("🔍 Testing list agents endpoint...")
    
    response = client.get("/autogen/agents")
    print(f"Response status: {response.status_code}")
    print(f"Response body: {response.text}")
    
    if response.status_code == 200:
        data = response.json()
        assert "agents" in data
        assert "total" in data
        assert "metadata" in data
        print(f"✅ Found {data['total']} agents: {data['agents']}")
    else:
        print(f"⚠️ List agents failed with status {response.status_code}: {response.text}")


def test_list_teams():
    """测试列出Team接口"""
    print("🔍 Testing list teams endpoint...")
    
    response = client.get("/autogen/teams")
    print(f"Response status: {response.status_code}")
    print(f"Response body: {response.text}")
    
    if response.status_code == 200:
        data = response.json()
        assert "teams" in data
        assert "total" in data
        assert "metadata" in data
        print(f"✅ Found {data['total']} teams: {data['teams']}")
    else:
        print(f"⚠️ List teams failed with status {response.status_code}: {response.text}")


def test_get_agent_info():
    """测试获取Agent信息接口"""
    print("🔍 Testing get agent info endpoint...")
    
    # 先获取可用的agent列表
    response = client.get("/autogen/agents")
    if response.status_code != 200:
        print("⚠️ Cannot get agent list, skipping agent info test")
        return
    
    agents_data = response.json()
    if not agents_data["agents"]:
        print("⚠️ No agents available, skipping agent info test")
        return
    
    # 测试第一个agent的信息
    agent_name = agents_data["agents"][0]
    response = client.get(f"/autogen/agents/{agent_name}")
    print(f"Response status: {response.status_code}")
    print(f"Response body: {response.text}")
    
    if response.status_code == 200:
        data = response.json()
        assert "name" in data
        assert "description" in data
        assert "capabilities" in data
        assert "metadata" in data
        print(f"✅ Got info for agent: {agent_name}")
    else:
        print(f"⚠️ Get agent info failed with status {response.status_code}: {response.text}")


def test_get_team_info():
    """测试获取Team信息接口"""
    print("🔍 Testing get team info endpoint...")
    
    # 先获取可用的team列表
    response = client.get("/autogen/teams")
    if response.status_code != 200:
        print("⚠️ Cannot get team list, skipping team info test")
        return
    
    teams_data = response.json()
    if not teams_data["teams"]:
        print("⚠️ No teams available, skipping team info test")
        return
    
    # 测试第一个team的信息
    team_name = teams_data["teams"][0]
    response = client.get(f"/autogen/teams/{team_name}")
    print(f"Response status: {response.status_code}")
    print(f"Response body: {response.text}")
    
    if response.status_code == 200:
        data = response.json()
        assert "name" in data
        assert "description" in data
        assert "agents" in data
        assert "capabilities" in data
        assert "metadata" in data
        print(f"✅ Got info for team: {team_name}")
    else:
        print(f"⚠️ Get team info failed with status {response.status_code}: {response.text}")


def test_run_agent():
    """测试执行Agent接口（模拟模式）"""
    print("🔍 Testing run agent endpoint...")
    
    # 先获取可用的agent列表
    response = client.get("/autogen/agents")
    if response.status_code != 200:
        print("⚠️ Cannot get agent list, skipping agent run test")
        return
    
    agents_data = response.json()
    if not agents_data["agents"]:
        print("⚠️ No agents available, skipping agent run test")
        return
    
    # 测试运行第一个agent
    agent_name = agents_data["agents"][0]
    request_data = {
        "name": agent_name,
        "message": "Hello, this is a test message",
        "model": "gpt-4o-mini",  # 使用真实模型进行测试
        "options": {
            "timeout": 10
        }
    }
    
    response = client.post("/autogen/run/agent", json=request_data)
    print(f"Response status: {response.status_code}")
    print(f"Response body: {response.text}")
    
    if response.status_code == 200:
        data = response.json()
        assert "status" in data
        assert "result" in data
        assert "metadata" in data
        print(f"✅ Agent run completed: {agent_name}")
    else:
        print(f"⚠️ Agent run failed with status {response.status_code}: {response.text}")


def test_run_team():
    """测试执行Team接口（模拟模式）"""
    print("🔍 Testing run team endpoint...")
    
    # 先获取可用的team列表
    response = client.get("/autogen/teams")
    if response.status_code != 200:
        print("⚠️ Cannot get team list, skipping team run test")
        return
    
    teams_data = response.json()
    if not teams_data["teams"]:
        print("⚠️ No teams available, skipping team run test")
        return
    
    # 测试运行第一个team
    team_name = teams_data["teams"][0]
    request_data = {
        "name": team_name,
        "message": "Hello, this is a test message for the team",
        "model": "gpt-4o-mini",  # 使用真实模型进行测试
        "options": {
            "timeout": 15,
            "max_rounds": 3
        }
    }
    
    response = client.post("/autogen/run/team", json=request_data)
    print(f"Response status: {response.status_code}")
    print(f"Response body: {response.text}")
    
    if response.status_code == 200:
        data = response.json()
        assert "status" in data
        assert "result" in data
        assert "metadata" in data
        print(f"✅ Team run completed: {team_name}")
    else:
        print(f"⚠️ Team run failed with status {response.status_code}: {response.text}")


def run_all_tests():
    """运行所有测试"""
    print("🚀 Starting API route tests...")
    print("=" * 50)
    
    try:
        # 基础接口测试
        test_health_check()
        test_root_endpoint()
        
        print("\n" + "=" * 50)
        
        # Agent接口测试
        test_list_agents()
        test_get_agent_info()
        test_run_agent()
        
        print("\n" + "=" * 50)
        
        # Team接口测试
        test_list_teams()
        test_get_team_info()
        test_run_team()
        
        print("\n" + "=" * 50)
        print("🎉 All API route tests completed!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_all_tests()
