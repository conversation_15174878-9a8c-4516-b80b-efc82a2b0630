"""
Agent服务层

提供Agent相关的业务逻辑服务，封装Agent管理器的调用。
支持Agent的创建、执行和管理功能。
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

try:
    from autogen_core import TRACE_LOGGER_NAME
    AUTOGEN_AVAILABLE = True
except ImportError:
    TRACE_LOGGER_NAME = "autogen_core.trace"
    AUTOGEN_AVAILABLE = False

from managers.agent_manager import AgentManager
from infrastructure.exceptions import ServiceError, AgentError

# 使用AutoGen的trace logger
logger = logging.getLogger(f"{TRACE_LOGGER_NAME}.agent_service")


class AgentService:
    """Agent服务类"""
    
    def __init__(self, agent_manager: Optional[AgentManager] = None):
        """
        初始化Agent服务
        
        Args:
            agent_manager: Agent管理器实例，如果为None则创建新实例
        """
        self.agent_manager = agent_manager or AgentManager()
        logger.info("AgentService initialized")
    
    async def run(self, name: str, message: str, model: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        执行Agent任务

        Args:
            name: Agent名称/类型
            message: 用户消息
            model: 模型别名（可选，AssistantAgent和SocietyOfMindAgent需要）
            **kwargs: 其他参数

        Returns:
            执行结果字典
        """
        start_time = datetime.now()

        try:
            model_info = f", model: {model}" if model else ""
            logger.info(f"Starting agent run: {name}{model_info}")

            # 验证参数
            self._validate_run_params(name, message, model)
            
            # 创建Agent实例
            agent = self._get_agent_instance(name, model, **kwargs)
            
            # 执行Agent任务
            result = await self._execute_agent(agent, message, **kwargs)
            
            # 计算执行时间
            execution_time = (datetime.now() - start_time).total_seconds()
            
            logger.info(f"Agent run completed: {name}, execution_time: {execution_time:.2f}s")
            
            return {
                "status": "success",
                "result": result,
                "metadata": {
                    "agent_name": name,
                    "model": model,
                    "message_length": len(message),
                    "execution_time": execution_time,
                    "timestamp": start_time.isoformat()
                }
            }
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"Agent run failed: {name}, error: {str(e)}, execution_time: {execution_time:.2f}s")
            raise ServiceError(f"Agent execution failed: {str(e)}")
    
    async def list_agents(self) -> Dict[str, Any]:
        """
        列出所有可用的Agent
        
        Returns:
            包含Agent列表和详细信息的字典
        """
        try:
            logger.info("Listing available agents")
            
            available_agents = self.agent_manager.list_available_agents()
            
            # 获取每个Agent的详细信息
            agents_info = []
            for agent_name in available_agents:
                try:
                    info = self.agent_manager.get_agent_info(agent_name)
                    agents_info.append({
                        "name": agent_name,
                        "factory_function": info.get("factory_function", "unknown"),
                        "module": info.get("module", "unknown"),
                        "signature": info.get("signature", "unknown")
                    })
                except Exception as e:
                    logger.warning(f"Failed to get info for agent {agent_name}: {e}")
                    agents_info.append({
                        "name": agent_name,
                        "factory_function": "unknown",
                        "module": "unknown",
                        "signature": "unknown",
                        "error": str(e)
                    })
            
            logger.info(f"Listed {len(available_agents)} agents")
            
            return {
                "status": "success",
                "agents": agents_info,
                "count": len(available_agents),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to list agents: {str(e)}")
            raise ServiceError(f"Failed to list agents: {str(e)}")
    
    async def get_agent_info(self, name: str) -> Dict[str, Any]:
        """
        获取指定Agent的详细信息
        
        Args:
            name: Agent名称
        
        Returns:
            Agent详细信息字典
        """
        try:
            logger.info(f"Getting agent info: {name}")
            
            # 验证Agent是否存在
            available_agents = self.agent_manager.list_available_agents()
            if name not in available_agents:
                raise AgentError(f"Agent '{name}' not found. Available agents: {available_agents}")
            
            # 获取Agent信息
            info = self.agent_manager.get_agent_info(name)
            
            logger.info(f"Retrieved agent info: {name}")
            
            return {
                "status": "success",
                "agent_info": info,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get agent info for {name}: {str(e)}")
            raise ServiceError(f"Failed to get agent info: {str(e)}")
    
    def _validate_run_params(self, name: str, message: str, model: Optional[str]) -> None:
        """
        验证运行参数

        Args:
            name: Agent名称
            message: 用户消息
            model: 模型别名（可选）

        Raises:
            ServiceError: 参数验证失败
        """
        if not name or not isinstance(name, str):
            raise ServiceError("Agent name must be a non-empty string")

        if not message or not isinstance(message, str):
            raise ServiceError("Message must be a non-empty string")

        if model is not None and (not isinstance(model, str) or not model.strip()):
            raise ServiceError("Model must be a non-empty string when provided")
        
        # 验证Agent是否存在
        available_agents = self.agent_manager.list_available_agents()
        if name not in available_agents:
            raise ServiceError(f"Agent '{name}' not found. Available agents: {available_agents}")
    
    def _get_agent_instance(self, name: str, model: Optional[str], **kwargs):
        """
        获取Agent实例

        Args:
            name: Agent名称
            model: 模型别名（可选）
            **kwargs: 其他参数

        Returns:
            Agent实例
        """
        try:
            # 从kwargs中提取Agent特定的参数
            agent_kwargs = kwargs.copy()
            
            # 移除服务层特定的参数
            service_params = ['timeout', 'max_retries', 'stream']
            for param in service_params:
                agent_kwargs.pop(param, None)
            
            # 创建Agent实例
            agent = self.agent_manager.create_agent(name, model, **agent_kwargs)
            
            logger.debug(f"Created agent instance: {name}, type: {type(agent)}")
            return agent
            
        except Exception as e:
            logger.error(f"Failed to create agent instance {name}: {str(e)}")
            raise AgentError(f"Failed to create agent instance: {str(e)}")
    
    async def _execute_agent(self, agent, message: str, **kwargs) -> Any:
        """
        执行Agent任务

        Args:
            agent: Agent实例
            message: 用户消息
            **kwargs: 其他参数

        Returns:
            Agent执行结果
        """
        try:
            # 获取执行参数
            timeout = kwargs.get('timeout', 30)  # 默认30秒超时
            stream = kwargs.get('stream', False)  # 是否使用流式输出

            # 如果AutoGen不可用，返回模拟结果
            if not AUTOGEN_AVAILABLE:
                logger.warning("AutoGen not available, returning mock result")
                await asyncio.sleep(0.1)  # 模拟执行时间
                return {
                    "content": f"Mock response from {getattr(agent, 'name', 'unknown_agent')} for message: {message[:50]}...",
                    "mock": True
                }

            # 使用AutoGen 0.6.2的正确API
            from autogen_core import CancellationToken
            from autogen_agentchat.messages import TextMessage

            # 创建取消令牌用于超时控制
            cancellation_token = CancellationToken()

            # 创建超时任务
            timeout_task = asyncio.create_task(asyncio.sleep(timeout))

            try:
                if stream:
                    # 使用流式执行
                    result = await self._execute_agent_stream(agent, message, cancellation_token)
                else:
                    # 使用标准执行
                    result = await self._execute_agent_run(agent, message, cancellation_token)

                # 取消超时任务
                timeout_task.cancel()
                return result

            except asyncio.TimeoutError:
                cancellation_token.cancel()
                raise ServiceError(f"Agent execution timeout after {timeout}s")

        except Exception as e:
            logger.error(f"Agent execution failed: {str(e)}")
            raise AgentError(f"Agent execution failed: {str(e)}")

    async def _execute_agent_run(self, agent, message: str, cancellation_token) -> Any:
        """使用agent.run()方法执行"""
        try:
            from autogen_agentchat.messages import TextMessage

            # 创建任务消息
            task_message = TextMessage(content=message, source="user")

            # 执行Agent任务
            result = await agent.run(task=task_message, cancellation_token=cancellation_token)

            # 提取结果
            if hasattr(result, 'messages') and result.messages:
                # 获取最后一条消息作为响应
                last_message = result.messages[-1]
                return {
                    "content": getattr(last_message, 'content', str(last_message)),
                    "source": getattr(last_message, 'source', 'agent'),
                    "type": getattr(last_message, 'type', 'unknown'),
                    "stop_reason": getattr(result, 'stop_reason', None),
                    "message_count": len(result.messages)
                }
            else:
                return {
                    "content": str(result),
                    "source": getattr(agent, 'name', 'agent'),
                    "type": "result"
                }

        except Exception as e:
            logger.error(f"Agent run execution failed: {str(e)}")
            raise

    async def _execute_agent_stream(self, agent, message: str, cancellation_token) -> Any:
        """使用agent.run_stream()方法执行"""
        try:
            from autogen_agentchat.messages import TextMessage

            # 创建任务消息
            task_message = TextMessage(content=message, source="user")

            # 执行Agent流式任务
            messages = []
            final_result = None

            async for item in agent.run_stream(task=task_message, cancellation_token=cancellation_token):
                # 检查是否是TaskResult（最后一个项目）
                if hasattr(item, 'messages') and hasattr(item, 'stop_reason'):
                    final_result = item
                else:
                    # 收集中间消息
                    messages.append(item)

            # 返回结果
            if final_result:
                return {
                    "content": getattr(final_result.messages[-1], 'content', str(final_result.messages[-1])) if final_result.messages else "No response",
                    "source": getattr(final_result.messages[-1], 'source', 'agent') if final_result.messages else 'agent',
                    "type": "stream_result",
                    "stop_reason": getattr(final_result, 'stop_reason', None),
                    "message_count": len(final_result.messages),
                    "stream_messages": len(messages)
                }
            else:
                return {
                    "content": "Stream completed without final result",
                    "source": getattr(agent, 'name', 'agent'),
                    "type": "stream_incomplete",
                    "stream_messages": len(messages)
                }

        except Exception as e:
            logger.error(f"Agent stream execution failed: {str(e)}")
            raise
