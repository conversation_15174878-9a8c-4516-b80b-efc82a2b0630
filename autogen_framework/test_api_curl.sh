#!/bin/bash

# AutoGen Multi-Agent Framework API测试脚本
# 使用curl测试所有API接口

echo "🚀 开始测试AutoGen Multi-Agent Framework API接口"
echo "=================================================="

BASE_URL="http://localhost:8000"

# 测试健康检查
echo "🔍 测试健康检查接口..."
curl -s -X GET "$BASE_URL/health"
echo ""

# 测试根接口
echo "🔍 测试根接口..."
curl -s -X GET "$BASE_URL/"
echo ""

# 测试列出Agent
echo "🔍 测试列出Agent接口..."
curl -s -X GET "$BASE_URL/autogen/agents"
echo ""

# 测试获取Agent信息
echo "🔍 测试获取Agent信息接口..."
curl -s -X GET "$BASE_URL/autogen/agents/code_reviewer"
echo ""

# 测试列出Team
echo "🔍 测试列出Team接口..."
curl -s -X GET "$BASE_URL/autogen/teams"
echo ""

# 测试获取Team信息
echo "🔍 测试获取Team信息接口..."
curl -s -X GET "$BASE_URL/autogen/teams/development"
echo ""

# 测试运行Agent（使用模拟模型）
echo "🔍 测试运行Agent接口（模拟模式）..."
curl -s -X POST "$BASE_URL/autogen/run/agent" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "code_reviewer",
    "message": "请审查这段代码：def hello(): print(\"Hello World\")",
    "model": "gpt-4o-mini",
    "options": {
      "timeout": 10
    }
  }'
echo ""

# 测试运行Team（使用模拟模型）
echo "🔍 测试运行Team接口（模拟模式）..."
curl -s -X POST "$BASE_URL/autogen/run/team" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "development",
    "message": "请开发一个计算斐波那契数列的Python函数",
    "model": "gpt-4o-mini",
    "options": {
      "timeout": 30,
      "max_rounds": 3
    }
  }'
echo ""

echo "=================================================="
echo "🎉 API接口测试完成！"
