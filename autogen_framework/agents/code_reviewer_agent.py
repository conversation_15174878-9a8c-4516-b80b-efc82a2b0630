"""
代码审查Agent

专业的代码审查Agent，能够分析代码质量、发现潜在问题并提供改进建议。
使用AutoGen 0.6.2的新API。
"""

from typing import Dict, Any, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

try:
    from autogen_agentchat.agents import AssistantAgent
    from autogen_core.models import ChatCompletionClient
    AUTOGEN_AVAILABLE = True
except ImportError:
    # 如果AutoGen不可用，使用模拟类
    AssistantAgent = None
    ChatCompletionClient = None
    AUTOGEN_AVAILABLE = False

from managers.model_manager import ModelManager
from managers.prompt_manager import get_prompt_manager


def create_code_reviewer_agent(
    model_alias: str,
    name: Optional[str] = None,
    model_manager: Optional[Any] = None,
    prompt_manager: Optional[Any] = None,
    **kwargs
) -> Any:
    """
    创建代码审查Agent

    Args:
        model_alias: 模型别名
        name: Agent名称，默认为"CodeReviewer"
        **kwargs: 其他配置参数

    Returns:
        AssistantAgent实例（如果AutoGen可用）或模拟对象
    """
    if not AUTOGEN_AVAILABLE:
        # 返回模拟对象用于测试
        class MockAgent:
            def __init__(self, name, system_message, model_client):
                self.name = name
                self.system_message = system_message
                self.model_client = model_client

        return MockAgent(
            name=name or "CodeReviewer",
            system_message="Mock code reviewer agent",
            model_client=f"mock_client_{model_alias}"
        )

    # 获取提示词
    if prompt_manager is None:
        prompt_manager = get_prompt_manager()
    system_prompt = prompt_manager.get_prompt("agents/code_reviewer", **kwargs)

    # 获取模型客户端
    if model_manager is None:
        model_manager = ModelManager()
    model_client = model_manager.get_model_client(model_alias)

    # 创建Agent
    agent = AssistantAgent(
        name=name or "CodeReviewer",
        model_client=model_client,
        system_message=system_prompt,
        description="专业的代码审查Agent，能够分析代码质量、发现潜在问题并提供改进建议。"
    )

    return agent


def create_senior_code_reviewer_agent(
    model_alias: str,
    name: Optional[str] = None,
    model_manager: Optional[Any] = None,
    prompt_manager: Optional[Any] = None,
    **kwargs
) -> Any:
    """
    创建高级代码审查Agent

    Args:
        model_alias: 模型别名
        name: Agent名称，默认为"SeniorCodeReviewer"
        **kwargs: 其他配置参数

    Returns:
        AssistantAgent实例（如果AutoGen可用）或模拟对象
    """
    if not AUTOGEN_AVAILABLE:
        # 返回模拟对象用于测试
        class MockAgent:
            def __init__(self, name, system_message, model_client):
                self.name = name
                self.system_message = system_message
                self.model_client = model_client

        return MockAgent(
            name=name or "SeniorCodeReviewer",
            system_message="Mock senior code reviewer agent",
            model_client=f"mock_client_{model_alias}"
        )

    # 为高级审查员添加特殊指令
    special_instructions = """
    作为高级代码审查专家，你需要：
    1. 关注架构设计和系统性问题
    2. 提供性能优化和可扩展性建议
    3. 识别安全漏洞和最佳实践违规
    4. 给出具体的重构建议
    """

    kwargs["special_instructions"] = special_instructions

    # 获取提示词 - 使用专门的高级审查员提示词
    if prompt_manager is None:
        prompt_manager = get_prompt_manager()
    system_prompt = prompt_manager.get_prompt("agents/senior_code_reviewer", **kwargs)

    # 获取模型客户端
    if model_manager is None:
        model_manager = ModelManager()
    model_client = model_manager.get_model_client(model_alias)

    # 创建Agent
    agent = AssistantAgent(
        name=name or "SeniorCodeReviewer",
        model_client=model_client,
        system_message=system_prompt,
        description="高级代码审查专家，专注于架构设计、性能优化和安全性分析。"
    )

    return agent
