"""
Agent管理器

负责Agent工厂函数的注册、管理和动态创建Agent实例。
支持从agents目录自动发现和注册Agent工厂函数。
使用AutoGen 0.6.2的新API。
"""

import importlib
import importlib.util
import inspect
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List, Callable

try:
    from autogen_agentchat.agents import (
        AssistantAgent,
        UserProxyAgent,
        CodeExecutorAgent,
        SocietyOfMindAgent,
        BaseChatAgent
    )
    from autogen_core import TRACE_LOGGER_NAME
    AUTOGEN_AVAILABLE = True
except ImportError:
    # 如果AutoGen不可用，使用模拟类
    AssistantAgent = None
    UserProxyAgent = None
    CodeExecutorAgent = None
    SocietyOfMindAgent = None
    BaseChatAgent = None
    TRACE_LOGGER_NAME = "autogen_core.trace"
    AUTOGEN_AVAILABLE = False

from .model_manager import ModelManager
from .prompt_manager import get_prompt_manager

# 使用AutoGen的trace logger
logger = logging.getLogger(f"{TRACE_LOGGER_NAME}.agent_manager")


class AgentManager:
    """Agent管理器"""
    
    def __init__(self, agents_dir: Optional[str] = None, model_manager: Optional[ModelManager] = None):
        """
        初始化Agent管理器
        
        Args:
            agents_dir: Agent定义文件目录，默认为 autogen_framework/agents
            model_manager: 模型管理器实例
        """
        if agents_dir is None:
            # 获取当前文件所在目录的父目录下的agents目录
            current_dir = Path(__file__).parent.parent
            agents_dir = current_dir / "agents"
        
        self.agents_dir = Path(agents_dir)
        self.model_manager = model_manager or ModelManager()
        self.prompt_manager = get_prompt_manager()
        
        # Agent工厂函数注册表
        self._factories: Dict[str, Callable] = {}
        
        # Agent实例缓存
        self._cache: Dict[str, Any] = {}
        
        # 确保agents目录存在
        self.agents_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"AgentManager initialized with directory: {self.agents_dir}")
        
        # 自动发现和注册Agent工厂函数
        self._auto_discover_agents()
    
    def _auto_discover_agents(self) -> None:
        """自动发现agents目录中的Agent工厂函数并注册"""
        if not self.agents_dir.exists():
            logger.warning(f"Agents directory not found: {self.agents_dir}")
            return
        
        # 扫描agents目录中的Python文件
        for py_file in self.agents_dir.glob("*_agent.py"):
            try:
                self._load_agent_module(py_file)
            except Exception as e:
                logger.error(f"Failed to load agent module {py_file}: {str(e)}")
    
    def _load_agent_module(self, py_file: Path) -> None:
        """
        加载Agent模块并注册工厂函数
        
        Args:
            py_file: Python文件路径
        """
        # 构建模块名
        module_name = py_file.stem  # 去掉.py扩展名
        
        # 动态导入模块
        spec = importlib.util.spec_from_file_location(module_name, py_file)
        if spec is None or spec.loader is None:
            logger.error(f"Cannot load module spec for {py_file}")
            return
        
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # 查找create_*_agent函数
        for name, obj in inspect.getmembers(module):
            if (inspect.isfunction(obj) and 
                name.startswith("create_") and 
                name.endswith("_agent")):
                
                # 提取agent名称（去掉create_和_agent）
                agent_name = name[7:-6]  # create_xxx_agent -> xxx
                
                # 注册工厂函数
                self.register_agent_factory(agent_name, obj)
                logger.info(f"Auto-registered agent factory: {agent_name} from {py_file}")
    
    def register_agent_factory(self, name: str, factory_func: Callable) -> None:
        """
        注册Agent工厂函数
        
        Args:
            name: Agent名称
            factory_func: 工厂函数，应该接受model_alias和**kwargs参数
        """
        if not callable(factory_func):
            raise ValueError(f"Factory function must be callable: {factory_func}")
        
        # 验证函数签名
        sig = inspect.signature(factory_func)
        if 'model_alias' not in sig.parameters:
            logger.warning(f"Factory function {name} should accept 'model_alias' parameter")
        
        self._factories[name] = factory_func
        logger.info(f"Registered agent factory: {name}")
    
    def create_agent(self, agent_type: str, model_alias: Optional[str] = None, **kwargs) -> Any:
        """
        创建Agent实例，支持不同类型的Agent初始化

        支持的Agent类型和常用参数：
        - AssistantAgent: name, model_client, tools, description, system_message,
                         handoffs, model_context, model_client_stream, reflect_on_tool_use,
                         max_tool_iterations, tool_call_summary_format, memory, metadata
        - UserProxyAgent: name, description, input_func
        - CodeExecutorAgent: name, code_executor, description, system_message
        - SocietyOfMindAgent: name, model_client, inner_team, description,
                             instruction, response_prompt

        Args:
            agent_type: Agent类型名称
            model_alias: 模型别名（可选，AssistantAgent和SocietyOfMindAgent需要）
            **kwargs: Agent特定参数，包括：
                - name (str): Agent名称
                - description (str): Agent描述
                - system_message (str): 系统消息
                - tools (List): 工具列表
                - handoffs (List): 交接配置
                - model_context: 模型上下文
                - model_client_stream (bool): 是否启用流式模式
                - reflect_on_tool_use (bool): 是否反思工具使用
                - max_tool_iterations (int): 最大工具迭代次数
                - tool_call_summary_format (str): 工具调用摘要格式
                - memory (Sequence): 内存存储
                - metadata (Dict): 元数据
                - input_func (Callable): 用户输入函数（UserProxyAgent）
                - code_executor: 代码执行器（CodeExecutorAgent）
                - inner_team: 内部团队（SocietyOfMindAgent）
                - instruction (str): 指令（SocietyOfMindAgent）
                - response_prompt (str): 响应提示（SocietyOfMindAgent）

        Returns:
            Agent实例
        """
        if agent_type not in self._factories:
            available = list(self._factories.keys())
            raise ValueError(f"Unknown agent: {agent_type}. Available agents: {available}")

        # 生成缓存键
        model_part = model_alias if model_alias else "no_model"
        cache_key = f"{agent_type}_{model_part}_{hash(str(sorted(kwargs.items())))}"

        # 检查缓存
        if cache_key in self._cache and not kwargs.get('force_create', False):
            logger.debug(f"Returning cached agent: {cache_key}")
            return self._cache[cache_key]

        try:
            # 调用工厂函数创建Agent，传入管理器实例
            factory_func = self._factories[agent_type]

            # 检查工厂函数是否接受管理器参数
            sig = inspect.signature(factory_func)
            factory_kwargs = kwargs.copy()

            # 如果工厂函数接受model_manager参数，传入实例
            if 'model_manager' in sig.parameters:
                factory_kwargs['model_manager'] = self.model_manager

            # 如果工厂函数接受prompt_manager参数，传入实例
            if 'prompt_manager' in sig.parameters:
                factory_kwargs['prompt_manager'] = self.prompt_manager

            agent = factory_func(model_alias=model_alias, **factory_kwargs)

            # 检查返回的Agent类型
            if AUTOGEN_AVAILABLE and BaseChatAgent:
                if not isinstance(agent, BaseChatAgent):
                    logger.warning(f"Factory function returned {type(agent)}, expected BaseChatAgent or its subclass")
                else:
                    logger.debug(f"Created agent of type: {type(agent).__name__}")
            elif not AUTOGEN_AVAILABLE:
                logger.debug(f"AutoGen not available, skipping type check for {type(agent)}")

            # 缓存Agent实例
            self._cache[cache_key] = agent

            model_info = f" with model: {model_alias}" if model_alias else " without model"
            logger.info(f"Created agent: {agent_type}{model_info}")
            return agent

        except Exception as e:
            logger.error(f"Failed to create agent {agent_type}: {str(e)}")
            raise ValueError(f"Failed to create agent {agent_type}: {str(e)}")

    def create_autogen_agent(
        self,
        agent_class: str,
        model_alias: Optional[str] = None,
        name: str = None,
        **kwargs
    ) -> Any:
        """
        直接创建AutoGen Agent实例，支持不同类型的Agent

        Args:
            agent_class: Agent类名 ('AssistantAgent', 'UserProxyAgent', 'CodeExecutorAgent', 'SocietyOfMindAgent')
            model_alias: 模型别名（可选，AssistantAgent和SocietyOfMindAgent需要）
            name: Agent名称（可选，如果为None则使用默认名称）
            **kwargs: Agent特定参数

        Returns:
            Agent实例
        """
        if not AUTOGEN_AVAILABLE:
            raise RuntimeError("AutoGen is not available")

        # 如果没有提供名称，使用默认名称
        if name is None:
            name = f"{agent_class}_{id(kwargs)}"

        # 根据Agent类型创建实例
        if agent_class == 'AssistantAgent':
            if not model_alias:
                raise ValueError("AssistantAgent requires model_alias parameter")
            model_client = self.model_manager.get_model_client(model_alias)
            return self._create_assistant_agent(name, model_client, **kwargs)
        elif agent_class == 'UserProxyAgent':
            return self._create_user_proxy_agent(name, **kwargs)
        elif agent_class == 'CodeExecutorAgent':
            return self._create_code_executor_agent(name, **kwargs)
        elif agent_class == 'SocietyOfMindAgent':
            if not model_alias:
                raise ValueError("SocietyOfMindAgent requires model_alias parameter")
            model_client = self.model_manager.get_model_client(model_alias)
            return self._create_society_of_mind_agent(name, model_client, **kwargs)
        else:
            raise ValueError(f"Unsupported agent class: {agent_class}")

    def _create_assistant_agent(self, name: str, model_client: Any, **kwargs) -> Any:
        """创建AssistantAgent实例"""
        # 常用参数显式传递
        agent_kwargs = {
            'name': name,
            'model_client': model_client,
        }

        # 常用参数
        if 'description' in kwargs:
            agent_kwargs['description'] = kwargs.pop('description')
        if 'system_message' in kwargs:
            agent_kwargs['system_message'] = kwargs.pop('system_message')
        if 'tools' in kwargs:
            agent_kwargs['tools'] = kwargs.pop('tools')
        if 'handoffs' in kwargs:
            agent_kwargs['handoffs'] = kwargs.pop('handoffs')
        if 'model_context' in kwargs:
            agent_kwargs['model_context'] = kwargs.pop('model_context')

        # 不常用参数隐式传递
        if 'model_client_stream' in kwargs:
            agent_kwargs['model_client_stream'] = kwargs.pop('model_client_stream')
        if 'reflect_on_tool_use' in kwargs:
            agent_kwargs['reflect_on_tool_use'] = kwargs.pop('reflect_on_tool_use')
        if 'max_tool_iterations' in kwargs:
            agent_kwargs['max_tool_iterations'] = kwargs.pop('max_tool_iterations')
        if 'tool_call_summary_format' in kwargs:
            agent_kwargs['tool_call_summary_format'] = kwargs.pop('tool_call_summary_format')
        if 'tool_call_summary_formatter' in kwargs:
            agent_kwargs['tool_call_summary_formatter'] = kwargs.pop('tool_call_summary_formatter')
        if 'output_content_type' in kwargs:
            agent_kwargs['output_content_type'] = kwargs.pop('output_content_type')
        if 'output_content_type_format' in kwargs:
            agent_kwargs['output_content_type_format'] = kwargs.pop('output_content_type_format')
        if 'memory' in kwargs:
            agent_kwargs['memory'] = kwargs.pop('memory')
        if 'metadata' in kwargs:
            agent_kwargs['metadata'] = kwargs.pop('metadata')
        if 'workbench' in kwargs:
            agent_kwargs['workbench'] = kwargs.pop('workbench')

        # 传递剩余参数
        agent_kwargs.update(kwargs)

        return AssistantAgent(**agent_kwargs)

    def _create_user_proxy_agent(self, name: str, **kwargs) -> Any:
        """创建UserProxyAgent实例"""
        agent_kwargs = {
            'name': name,
        }

        # 常用参数
        if 'description' in kwargs:
            agent_kwargs['description'] = kwargs.pop('description')
        if 'input_func' in kwargs:
            agent_kwargs['input_func'] = kwargs.pop('input_func')

        # 传递剩余参数
        agent_kwargs.update(kwargs)

        return UserProxyAgent(**agent_kwargs)

    def _create_code_executor_agent(self, name: str, **kwargs) -> Any:
        """创建CodeExecutorAgent实例"""
        agent_kwargs = {
            'name': name,
        }

        # 常用参数
        if 'code_executor' in kwargs:
            agent_kwargs['code_executor'] = kwargs.pop('code_executor')
        if 'description' in kwargs:
            agent_kwargs['description'] = kwargs.pop('description')
        if 'system_message' in kwargs:
            agent_kwargs['system_message'] = kwargs.pop('system_message')

        # 传递剩余参数
        agent_kwargs.update(kwargs)

        return CodeExecutorAgent(**agent_kwargs)

    def _create_society_of_mind_agent(self, name: str, model_client: Any, **kwargs) -> Any:
        """创建SocietyOfMindAgent实例"""
        agent_kwargs = {
            'name': name,
            'model_client': model_client,
        }

        # 常用参数
        if 'inner_team' in kwargs:
            agent_kwargs['inner_team'] = kwargs.pop('inner_team')
        if 'description' in kwargs:
            agent_kwargs['description'] = kwargs.pop('description')
        if 'instruction' in kwargs:
            agent_kwargs['instruction'] = kwargs.pop('instruction')
        if 'response_prompt' in kwargs:
            agent_kwargs['response_prompt'] = kwargs.pop('response_prompt')

        # 传递剩余参数
        agent_kwargs.update(kwargs)

        return SocietyOfMindAgent(**agent_kwargs)

    def list_available_agents(self) -> List[str]:
        """
        列出所有可用的Agent
        
        Returns:
            Agent名称列表
        """
        return list(self._factories.keys())
    
    def get_agent_info(self, agent_type: str) -> Dict[str, Any]:
        """
        获取Agent信息

        Args:
            agent_type: Agent类型名称

        Returns:
            Agent信息字典
        """
        if agent_type not in self._factories:
            raise ValueError(f"Unknown agent: {agent_type}")

        factory_func = self._factories[agent_type]
        sig = inspect.signature(factory_func)

        return {
            "name": agent_type,
            "factory_function": factory_func.__name__,
            "module": factory_func.__module__,
            "signature": str(sig),
            "docstring": factory_func.__doc__
        }
    
    def clear_cache(self) -> None:
        """清空Agent缓存"""
        self._cache.clear()
        logger.info("Agent cache cleared")
    
    def reload_agents(self) -> None:
        """重新加载所有Agent定义"""
        # 清空注册表和缓存
        self._factories.clear()
        self._cache.clear()
        
        # 重新发现和注册
        self._auto_discover_agents()
        
        logger.info("All agents reloaded")


# 全局实例
_default_manager = None


def get_agent_manager() -> AgentManager:
    """获取默认的Agent管理器实例"""
    global _default_manager
    if _default_manager is None:
        _default_manager = AgentManager()
    return _default_manager
