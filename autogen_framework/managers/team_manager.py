"""
Team管理器

负责Team工厂函数的注册、管理和动态创建Team实例。
支持从teams目录自动发现和注册Team工厂函数。
Team通过Agent管理器获取Agent实例，支持复杂的工作流定义。
"""

import importlib
import importlib.util
import inspect
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List, Callable

try:
    from autogen_agentchat.teams import (
        BaseGroupChat,
        RoundRobinGroupChat,
        SelectorGroupChat,
        Swarm,
        GraphFlow,
        MagenticOneGroupChat
    )
    from autogen_core import TRACE_LOGGER_NAME
    AUTOGEN_AVAILABLE = True
except ImportError:
    # 如果AutoGen不可用，使用模拟类
    BaseGroupChat = None
    RoundRobinGroupChat = None
    SelectorGroupChat = None
    Swarm = None
    GraphFlow = None
    MagenticOneGroupChat = None
    TRACE_LOGGER_NAME = "autogen_core.trace"
    AUTOGEN_AVAILABLE = False

from .agent_manager import AgentManager
from .model_manager import ModelManager

# 使用AutoGen的trace logger
logger = logging.getLogger(f"{TRACE_LOGGER_NAME}.team_manager")


class TeamManager:
    """Team管理器"""
    
    def __init__(self, teams_dir: Optional[str] = None, agent_manager: Optional[AgentManager] = None):
        """
        初始化Team管理器
        
        Args:
            teams_dir: Team定义文件目录，默认为 autogen_framework/teams
            agent_manager: Agent管理器实例
        """
        if teams_dir is None:
            # 获取当前文件所在目录的父目录下的teams目录
            current_dir = Path(__file__).parent.parent
            teams_dir = current_dir / "teams"
        
        self.teams_dir = Path(teams_dir)
        self.agent_manager = agent_manager or AgentManager()
        self.model_manager = ModelManager()
        
        # Team工厂函数注册表
        self._factories: Dict[str, Callable] = {}
        
        # Team实例缓存
        self._cache: Dict[str, Any] = {}
        
        # 确保teams目录存在
        self.teams_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"TeamManager initialized with directory: {self.teams_dir}")
        
        # 自动发现和注册Team工厂函数
        self._auto_discover_teams()
    
    def _auto_discover_teams(self) -> None:
        """自动发现teams目录中的Team工厂函数并注册"""
        if not self.teams_dir.exists():
            logger.warning(f"Teams directory not found: {self.teams_dir}")
            return
        
        # 扫描teams目录中的Python文件
        for py_file in self.teams_dir.glob("*_team.py"):
            try:
                self._load_team_module(py_file)
            except Exception as e:
                logger.error(f"Failed to load team module {py_file}: {str(e)}")
    
    def _load_team_module(self, py_file: Path) -> None:
        """
        加载Team模块并注册工厂函数
        
        Args:
            py_file: Python文件路径
        """
        # 构建模块名
        module_name = py_file.stem  # 去掉.py扩展名
        
        # 动态导入模块
        spec = importlib.util.spec_from_file_location(module_name, py_file)
        if spec is None or spec.loader is None:
            logger.error(f"Cannot load module spec for {py_file}")
            return
        
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # 查找create_*_team函数
        for name, obj in inspect.getmembers(module):
            if (inspect.isfunction(obj) and 
                name.startswith("create_") and 
                name.endswith("_team")):
                
                # 提取team名称（去掉create_和_team）
                team_name = name[7:-5]  # create_xxx_team -> xxx
                
                # 注册工厂函数
                self.register_team_factory(team_name, obj)
                logger.info(f"Auto-registered team factory: {team_name} from {py_file}")
    
    def register_team_factory(self, name: str, factory_func: Callable) -> None:
        """
        注册Team工厂函数
        
        Args:
            name: Team名称
            factory_func: 工厂函数，应该接受model_alias、agent_manager和**kwargs参数
        """
        if not callable(factory_func):
            raise ValueError(f"Factory function must be callable: {factory_func}")
        
        # 验证函数签名
        sig = inspect.signature(factory_func)
        if 'model_alias' not in sig.parameters:
            logger.warning(f"Factory function {name} should accept 'model_alias' parameter")
        if 'agent_manager' not in sig.parameters:
            logger.warning(f"Factory function {name} should accept 'agent_manager' parameter")
        
        self._factories[name] = factory_func
        logger.info(f"Registered team factory: {name}")
    
    def create_team(self, name: str, model_alias: str, **kwargs) -> Any:
        """
        创建Team实例，支持不同类型的Team初始化

        支持的Team类型和常用参数：
        - RoundRobinGroupChat: participants, termination_condition, max_turns
        - SelectorGroupChat: participants, model_client, termination_condition,
                             selector_prompt, allow_repeated_speaker, max_selector_attempts
        - Swarm: participants, termination_condition, max_turns
        - GraphFlow: participants, graph, termination_condition, max_turns
        - MagenticOneGroupChat: participants, model_client, termination_condition

        Args:
            name: Team名称
            model_alias: 模型别名
            **kwargs: Team特定参数，包括：
                - participants (List): 参与者列表（必需）
                - termination_condition: 终止条件
                - max_turns (int): 最大轮次
                - model_client: 模型客户端（SelectorGroupChat等需要）
                - selector_prompt (str): 选择器提示（SelectorGroupChat）
                - allow_repeated_speaker (bool): 是否允许重复发言者（SelectorGroupChat）
                - max_selector_attempts (int): 最大选择器尝试次数（SelectorGroupChat）
                - graph: 图结构（GraphFlow）
                - runtime: 运行时环境
                - custom_message_types: 自定义消息类型
                - emit_team_events (bool): 是否发出团队事件

        Returns:
            Team实例
        """
        if name not in self._factories:
            available = list(self._factories.keys())
            raise ValueError(f"Unknown team: {name}. Available teams: {available}")

        # 生成缓存键
        cache_key = f"{name}_{model_alias}_{hash(str(sorted(kwargs.items())))}"

        # 检查缓存
        if cache_key in self._cache and not kwargs.get('force_create', False):
            logger.debug(f"Returning cached team: {cache_key}")
            return self._cache[cache_key]

        try:
            # 调用工厂函数创建Team，传入管理器实例
            factory_func = self._factories[name]

            # 检查工厂函数是否接受管理器参数
            sig = inspect.signature(factory_func)
            factory_kwargs = kwargs.copy()

            # 如果工厂函数接受agent_manager参数，传入实例
            if 'agent_manager' in sig.parameters:
                factory_kwargs['agent_manager'] = self.agent_manager

            # 如果工厂函数接受model_manager参数，传入实例
            if 'model_manager' in sig.parameters:
                factory_kwargs['model_manager'] = self.model_manager

            team = factory_func(model_alias=model_alias, **factory_kwargs)

            # 检查返回的team类型
            if AUTOGEN_AVAILABLE and BaseGroupChat:
                if not isinstance(team, BaseGroupChat):
                    logger.warning(f"Factory function returned {type(team)}, expected BaseGroupChat or its subclass")
                else:
                    logger.debug(f"Created team of type: {type(team).__name__}")
            elif not AUTOGEN_AVAILABLE:
                logger.debug(f"AutoGen not available, skipping type check for {type(team)}")

            # 缓存Team实例
            self._cache[cache_key] = team

            logger.info(f"Created team: {name} with model: {model_alias}")
            return team

        except Exception as e:
            logger.error(f"Failed to create team {name}: {str(e)}")
            raise ValueError(f"Failed to create team {name}: {str(e)}")

    def create_autogen_team(
        self,
        team_class: str,
        model_alias: str,
        participants: List[Any],
        **kwargs
    ) -> Any:
        """
        直接创建AutoGen Team实例，支持不同类型的Team

        Args:
            team_class: Team类名 ('RoundRobinGroupChat', 'SelectorGroupChat', 'Swarm', 'GraphFlow', 'MagenticOneGroupChat')
            model_alias: 模型别名
            participants: 参与者列表
            **kwargs: Team特定参数

        Returns:
            Team实例
        """
        if not AUTOGEN_AVAILABLE:
            raise RuntimeError("AutoGen is not available")

        # 根据Team类型创建实例
        if team_class == 'RoundRobinGroupChat':
            return self._create_round_robin_group_chat(participants, **kwargs)
        elif team_class == 'SelectorGroupChat':
            model_client = self.model_manager.get_model_client(model_alias)
            return self._create_selector_group_chat(participants, model_client, **kwargs)
        elif team_class == 'Swarm':
            return self._create_swarm(participants, **kwargs)
        elif team_class == 'GraphFlow':
            return self._create_graph_flow(participants, **kwargs)
        elif team_class == 'MagenticOneGroupChat':
            model_client = self.model_manager.get_model_client(model_alias)
            return self._create_magentic_one_group_chat(participants, model_client, **kwargs)
        else:
            raise ValueError(f"Unsupported team class: {team_class}")

    def _create_round_robin_group_chat(self, participants: List[Any], **kwargs) -> Any:
        """创建RoundRobinGroupChat实例"""
        team_kwargs = {
            'participants': participants,
        }

        # 常用参数
        if 'termination_condition' in kwargs:
            team_kwargs['termination_condition'] = kwargs.pop('termination_condition')
        if 'max_turns' in kwargs:
            team_kwargs['max_turns'] = kwargs.pop('max_turns')
        if 'runtime' in kwargs:
            team_kwargs['runtime'] = kwargs.pop('runtime')
        if 'custom_message_types' in kwargs:
            team_kwargs['custom_message_types'] = kwargs.pop('custom_message_types')
        if 'emit_team_events' in kwargs:
            team_kwargs['emit_team_events'] = kwargs.pop('emit_team_events')

        # 传递剩余参数
        team_kwargs.update(kwargs)

        return RoundRobinGroupChat(**team_kwargs)

    def _create_selector_group_chat(self, participants: List[Any], model_client: Any, **kwargs) -> Any:
        """创建SelectorGroupChat实例"""
        team_kwargs = {
            'participants': participants,
            'model_client': model_client,
        }

        # 常用参数显式传递
        if 'termination_condition' in kwargs:
            team_kwargs['termination_condition'] = kwargs.pop('termination_condition')
        if 'selector_prompt' in kwargs:
            team_kwargs['selector_prompt'] = kwargs.pop('selector_prompt')
        if 'allow_repeated_speaker' in kwargs:
            team_kwargs['allow_repeated_speaker'] = kwargs.pop('allow_repeated_speaker')
        if 'max_selector_attempts' in kwargs:
            team_kwargs['max_selector_attempts'] = kwargs.pop('max_selector_attempts')

        # 不常用参数隐式传递
        if 'max_turns' in kwargs:
            team_kwargs['max_turns'] = kwargs.pop('max_turns')
        if 'runtime' in kwargs:
            team_kwargs['runtime'] = kwargs.pop('runtime')
        if 'selector_func' in kwargs:
            team_kwargs['selector_func'] = kwargs.pop('selector_func')
        if 'candidate_func' in kwargs:
            team_kwargs['candidate_func'] = kwargs.pop('candidate_func')
        if 'custom_message_types' in kwargs:
            team_kwargs['custom_message_types'] = kwargs.pop('custom_message_types')
        if 'emit_team_events' in kwargs:
            team_kwargs['emit_team_events'] = kwargs.pop('emit_team_events')
        if 'model_client_streaming' in kwargs:
            team_kwargs['model_client_streaming'] = kwargs.pop('model_client_streaming')
        if 'model_context' in kwargs:
            team_kwargs['model_context'] = kwargs.pop('model_context')

        # 传递剩余参数
        team_kwargs.update(kwargs)

        return SelectorGroupChat(**team_kwargs)

    def _create_swarm(self, participants: List[Any], **kwargs) -> Any:
        """创建Swarm实例"""
        team_kwargs = {
            'participants': participants,
        }

        # 常用参数
        if 'termination_condition' in kwargs:
            team_kwargs['termination_condition'] = kwargs.pop('termination_condition')
        if 'max_turns' in kwargs:
            team_kwargs['max_turns'] = kwargs.pop('max_turns')
        if 'runtime' in kwargs:
            team_kwargs['runtime'] = kwargs.pop('runtime')
        if 'custom_message_types' in kwargs:
            team_kwargs['custom_message_types'] = kwargs.pop('custom_message_types')
        if 'emit_team_events' in kwargs:
            team_kwargs['emit_team_events'] = kwargs.pop('emit_team_events')

        # 传递剩余参数
        team_kwargs.update(kwargs)

        return Swarm(**team_kwargs)

    def _create_graph_flow(self, participants: List[Any], **kwargs) -> Any:
        """创建GraphFlow实例"""
        # graph是必需参数
        if 'graph' not in kwargs:
            raise ValueError("graph is required for GraphFlow. Please provide a graph instance.")

        team_kwargs = {
            'participants': participants,
            'graph': kwargs.pop('graph'),
        }

        # 常用参数
        if 'termination_condition' in kwargs:
            team_kwargs['termination_condition'] = kwargs.pop('termination_condition')
        if 'max_turns' in kwargs:
            team_kwargs['max_turns'] = kwargs.pop('max_turns')
        if 'runtime' in kwargs:
            team_kwargs['runtime'] = kwargs.pop('runtime')
        if 'custom_message_types' in kwargs:
            team_kwargs['custom_message_types'] = kwargs.pop('custom_message_types')
        if 'emit_team_events' in kwargs:
            team_kwargs['emit_team_events'] = kwargs.pop('emit_team_events')

        # 传递剩余参数
        team_kwargs.update(kwargs)

        return GraphFlow(**team_kwargs)

    def _create_magentic_one_group_chat(self, participants: List[Any], model_client: Any, **kwargs) -> Any:
        """创建MagenticOneGroupChat实例"""
        team_kwargs = {
            'participants': participants,
            'model_client': model_client,
        }

        # 常用参数
        if 'termination_condition' in kwargs:
            team_kwargs['termination_condition'] = kwargs.pop('termination_condition')
        if 'max_turns' in kwargs:
            team_kwargs['max_turns'] = kwargs.pop('max_turns')
        if 'runtime' in kwargs:
            team_kwargs['runtime'] = kwargs.pop('runtime')
        if 'custom_message_types' in kwargs:
            team_kwargs['custom_message_types'] = kwargs.pop('custom_message_types')
        if 'emit_team_events' in kwargs:
            team_kwargs['emit_team_events'] = kwargs.pop('emit_team_events')

        # 传递剩余参数
        team_kwargs.update(kwargs)

        return MagenticOneGroupChat(**team_kwargs)

    def list_available_teams(self) -> List[str]:
        """
        列出所有可用的Team
        
        Returns:
            Team名称列表
        """
        return list(self._factories.keys())
    
    def get_team_info(self, name: str) -> Dict[str, Any]:
        """
        获取Team信息
        
        Args:
            name: Team名称
            
        Returns:
            Team信息字典
        """
        if name not in self._factories:
            raise ValueError(f"Unknown team: {name}")
        
        factory_func = self._factories[name]
        sig = inspect.signature(factory_func)
        
        return {
            "name": name,
            "factory_function": factory_func.__name__,
            "module": factory_func.__module__,
            "signature": str(sig),
            "docstring": factory_func.__doc__
        }
    
    def clear_cache(self) -> None:
        """清空Team缓存"""
        self._cache.clear()
        logger.info("Team cache cleared")
    
    def reload_teams(self) -> None:
        """重新加载所有Team定义"""
        # 清空注册表和缓存
        self._factories.clear()
        self._cache.clear()
        
        # 重新发现和注册
        self._auto_discover_teams()
        
        logger.info("All teams reloaded")


# 全局实例
_default_manager = None


def get_team_manager() -> TeamManager:
    """获取默认的Team管理器实例"""
    global _default_manager
    if _default_manager is None:
        _default_manager = TeamManager()
    return _default_manager
