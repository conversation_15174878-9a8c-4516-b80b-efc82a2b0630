# AutoGen Team 类型支持文档

本文档描述了 AutoGen 多 Agent 框架对不同类型 Team 的支持，包括参数定义、使用方式和最佳实践。

## 支持的 Team 类型

### 1. RoundRobinGroupChat

**描述**: 轮询式群聊团队，参与者按顺序轮流发言。

**常用参数**（显式传递）:
- `participants` (List): 参与者列表（必需）
- `termination_condition`: 终止条件
- `max_turns` (int): 最大轮次

**不常用参数**（隐式传递）:
- `runtime`: 运行时环境
- `custom_message_types` (List): 自定义消息类型
- `emit_team_events` (bool): 是否发出团队事件，默认 False

**使用示例**:
```python
from teams.generic_team import create_round_robin_group_chat_team

team = create_round_robin_group_chat_team(
    model_alias='gpt-4',
    participants=[agent1, agent2, agent3],
    max_turns=10,
    emit_team_events=True
)
```

### 2. SelectorGroupChat

**描述**: 选择器群聊团队，通过模型选择下一个发言者。

**常用参数**（显式传递）:
- `participants` (List): 参与者列表（必需）
- `model_client` (ChatCompletionClient): 模型客户端（必需）
- `termination_condition`: 终止条件
- `selector_prompt` (str): 选择器提示
- `allow_repeated_speaker` (bool): 是否允许重复发言者，默认 False
- `max_selector_attempts` (int): 最大选择器尝试次数，默认 3

**不常用参数**（隐式传递）:
- `max_turns` (int): 最大轮次
- `runtime`: 运行时环境
- `selector_func` (Callable): 自定义选择器函数
- `candidate_func` (Callable): 候选者函数
- `custom_message_types` (List): 自定义消息类型
- `emit_team_events` (bool): 是否发出团队事件
- `model_client_streaming` (bool): 是否启用模型客户端流式传输
- `model_context`: 模型上下文

**使用示例**:
```python
from teams.generic_team import create_selector_group_chat_team

team = create_selector_group_chat_team(
    model_alias='gpt-4',
    participants=[agent1, agent2, agent3],
    selector_prompt='Choose the most appropriate agent from {participants} to respond.',
    allow_repeated_speaker=True,
    max_selector_attempts=5
)
```

### 3. Swarm

**描述**: 蜂群式团队，支持动态交接和协作。

**常用参数**（显式传递）:
- `participants` (List): 参与者列表（必需）
- `termination_condition`: 终止条件
- `max_turns` (int): 最大轮次

**不常用参数**（隐式传递）:
- `runtime`: 运行时环境
- `custom_message_types` (List): 自定义消息类型
- `emit_team_events` (bool): 是否发出团队事件

**使用示例**:
```python
from teams.generic_team import create_swarm_team

# 创建带交接配置的Agent
agent1 = create_assistant_agent(model_alias='gpt-4', name='Agent1', handoffs=['Agent2'])
agent2 = create_assistant_agent(model_alias='gpt-4', name='Agent2')

team = create_swarm_team(
    model_alias='gpt-4',
    participants=[agent1, agent2],
    max_turns=20
)
```

### 4. GraphFlow

**描述**: 图流式团队，基于有向图结构控制对话流程。

**常用参数**（显式传递）:
- `participants` (List): 参与者列表（必需）
- `graph`: 图结构（必需参数）
- `termination_condition`: 终止条件
- `max_turns` (int): 最大轮次

**不常用参数**（隐式传递）:
- `runtime`: 运行时环境
- `custom_message_types` (List): 自定义消息类型
- `emit_team_events` (bool): 是否发出团队事件

**使用示例**:
```python
from teams.generic_team import create_graph_flow_team
from autogen_agentchat.teams import DiGraphBuilder

# 创建图结构
builder = DiGraphBuilder()
builder.add_node('agent1', agent1)
builder.add_node('agent2', agent2)
builder.add_edge('agent1', 'agent2')
builder.set_entry_point('agent1')
graph = builder.build()

team = create_graph_flow_team(
    model_alias='gpt-4',
    participants=[agent1, agent2],
    graph=graph,
    max_turns=15
)
```

### 5. MagenticOneGroupChat

**描述**: MagenticOne 风格的群聊团队，专为复杂任务协作设计。

**常用参数**（显式传递）:
- `participants` (List): 参与者列表（必需）
- `model_client` (ChatCompletionClient): 模型客户端（必需）
- `termination_condition`: 终止条件

**不常用参数**（隐式传递）:
- `max_turns` (int): 最大轮次
- `runtime`: 运行时环境
- `custom_message_types` (List): 自定义消息类型
- `emit_team_events` (bool): 是否发出团队事件

**使用示例**:
```python
from teams.generic_team import create_magentic_one_group_chat_team

# 创建MagenticOne风格的Agent
orchestrator = create_assistant_agent(model_alias='gpt-4', name='Orchestrator')
web_surfer = create_assistant_agent(model_alias='gpt-4', name='WebSurfer')
coder = create_assistant_agent(model_alias='gpt-4', name='Coder')

team = create_magentic_one_group_chat_team(
    model_alias='gpt-4',
    participants=[orchestrator, web_surfer, coder],
    max_turns=30
)
```

## Team 管理器集成

### 直接创建 Team

```python
from managers.team_manager import get_team_manager

team_manager = get_team_manager()

# 直接创建不同类型的 Team
round_robin_team = team_manager.create_autogen_team(
    team_class='RoundRobinGroupChat',
    model_alias='gpt-4',
    participants=[agent1, agent2],
    max_turns=10
)

selector_team = team_manager.create_autogen_team(
    team_class='SelectorGroupChat',
    model_alias='gpt-4',
    participants=[agent1, agent2, agent3],
    allow_repeated_speaker=True
)
```

### 通过工厂函数创建

```python
# 注册通用 Team 工厂
from teams.generic_team import (
    create_round_robin_group_chat_team,
    create_selector_group_chat_team,
    create_swarm_team,
    create_graph_flow_team,
    create_magentic_one_group_chat_team
)

team_manager.register_team_factory('generic_round_robin', create_round_robin_group_chat_team)
team_manager.register_team_factory('generic_selector', create_selector_group_chat_team)

# 通过工厂创建
team = team_manager.create_team(
    name='generic_selector',
    model_alias='gpt-4',
    max_turns=15
)
```

## 最佳实践

### 1. 参数传递策略

- **常用参数**: 显式传递，提供清晰的接口
- **不常用参数**: 隐式传递，保持接口简洁
- **必需参数**: 明确标识并提供默认值或错误提示

### 2. 团队组合建议

- **RoundRobinGroupChat**: 适用于需要公平轮流发言的场景
- **SelectorGroupChat**: 适用于需要智能选择发言者的复杂协作
- **Swarm**: 适用于需要动态交接和灵活协作的场景
- **GraphFlow**: 适用于有明确流程控制需求的结构化任务
- **MagenticOneGroupChat**: 适用于复杂的多步骤任务协作

### 3. 终止条件管理

```python
from autogen_agentchat.base import TerminationCondition

# 使用内置终止条件
termination = TerminationCondition.max_turns(10)

# 或使用自定义终止条件
def custom_termination(messages):
    return len(messages) > 5 and "TERMINATE" in messages[-1].content

team = create_selector_group_chat_team(
    model_alias='gpt-4',
    termination_condition=custom_termination
)
```

### 4. 图结构设计（GraphFlow）

```python
from autogen_agentchat.teams import DiGraphBuilder

def create_linear_graph(agents):
    """创建线性图结构"""
    builder = DiGraphBuilder()
    for i, agent in enumerate(agents):
        builder.add_node(agent.name, agent)
        if i > 0:
            builder.add_edge(agents[i-1].name, agent.name)
    builder.set_entry_point(agents[0].name)
    return builder.build()

def create_hub_graph(hub_agent, spoke_agents):
    """创建中心辐射图结构"""
    builder = DiGraphBuilder()
    builder.add_node(hub_agent.name, hub_agent)
    for agent in spoke_agents:
        builder.add_node(agent.name, agent)
        builder.add_edge(hub_agent.name, agent.name)
        builder.add_edge(agent.name, hub_agent.name)
    builder.set_entry_point(hub_agent.name)
    return builder.build()
```

## 扩展支持

### 添加新的 Team 类型

1. 在 `generic_team.py` 中添加新的创建函数
2. 参考官方文档定义参数
3. 实现常用参数显式传递，不常用参数隐式传递
4. 更新测试用例

### 自定义 Team 工厂

```python
def create_custom_team(
    model_alias: str,
    participants: List,
    custom_param: str = "default",
    **kwargs
) -> Any:
    """创建自定义 Team"""
    # 实现自定义逻辑
    pass

# 注册到 Team 管理器
team_manager.register_team_factory('custom', create_custom_team)
```

## 技术细节

- **AutoGen 版本**: 0.6.2
- **支持的 Team 基类**: BaseGroupChat
- **模型客户端**: ChatCompletionClient
- **图结构**: DiGraph, DiGraphBuilder
- **终止条件**: TerminationCondition
- **日志系统**: AutoGen trace logger

## 参考资料

- [AutoGen 官方文档 - Teams](https://microsoft.github.io/autogen/stable/reference/python/autogen_agentchat.teams.html)
- [AutoGen 团队教程](https://microsoft.github.io/autogen/stable/user-guide/agentchat-user-guide/tutorial/teams.html)
- [AutoGen 图流文档](https://microsoft.github.io/autogen/stable/user-guide/agentchat-user-guide/tutorial/graph-flow.html)
