# AutoGen Agent 类型支持文档

本文档描述了 AutoGen 多 Agent 框架对不同类型 Agent 的支持，包括参数定义、使用方式和最佳实践。

## 支持的 Agent 类型

### 1. AssistantAgent

**描述**: 提供工具使用能力的助手 Agent，是最常用的 Agent 类型。

**常用参数**（显式传递）:
- `name` (str): Agent 名称
- `model_client` (ChatCompletionClient): 模型客户端
- `description` (str): Agent 描述，默认为 "An agent that provides assistance with ability to use tools."
- `system_message` (str): 系统消息
- `tools` (List): 工具列表
- `handoffs` (List): 交接配置

**不常用参数**（隐式传递）:
- `model_context`: 模型上下文
- `model_client_stream` (bool): 是否启用流式模式，默认 False
- `reflect_on_tool_use` (bool): 是否反思工具使用
- `max_tool_iterations` (int): 最大工具迭代次数，默认 1
- `tool_call_summary_format` (str): 工具调用摘要格式，默认 "{result}"
- `tool_call_summary_formatter` (Callable): 工具调用摘要格式化器
- `output_content_type`: 输出内容类型
- `output_content_type_format` (str): 输出内容类型格式
- `memory` (Sequence): 内存存储
- `metadata` (Dict[str, str]): 元数据
- `workbench`: 工作台

**使用示例**:
```python
from agents.generic_agent import create_assistant_agent

agent = create_assistant_agent(
    model_alias='gpt-4',
    name='MyAssistant',
    description='A helpful assistant',
    system_message='You are a helpful AI assistant.',
    tools=[my_tool],
    max_tool_iterations=3,
    model_client_stream=True
)
```

### 2. UserProxyAgent

**描述**: 代表人类用户的 Agent，通过输入函数与用户交互。

**常用参数**:
- `name` (str): Agent 名称
- `description` (str): Agent 描述，默认为 "A human user"
- `input_func` (Callable): 用户输入函数

**使用示例**:
```python
from agents.generic_agent import create_user_proxy_agent

def my_input_func(prompt):
    return input(f"User: {prompt}")

agent = create_user_proxy_agent(
    model_alias='gpt-4',  # 保持接口一致性
    name='UserProxy',
    description='Human user interface',
    input_func=my_input_func
)
```

### 3. CodeExecutorAgent

**描述**: 执行代码块的 Agent，用于运行和分析代码。

**常用参数**:
- `name` (str): Agent 名称
- `code_executor`: 代码执行器（必需参数）
- `description` (str): Agent 描述，默认为 "An agent that executes code blocks."
- `system_message` (str): 系统消息

**使用示例**:
```python
from agents.generic_agent import create_code_executor_agent
from autogen_ext.code_executors.local import LocalCommandLineCodeExecutor

executor = LocalCommandLineCodeExecutor()

agent = create_code_executor_agent(
    model_alias='gpt-4',
    name='CodeExecutor',
    code_executor=executor,
    description='Safe code execution agent',
    system_message='You execute code safely and report results.'
)
```

### 4. SocietyOfMindAgent

**描述**: 使用内部团队生成响应的 Agent，实现复杂的多 Agent 协作。

**常用参数**:
- `name` (str): Agent 名称
- `model_client` (ChatCompletionClient): 模型客户端
- `team`: 内部团队（必需参数）
- `description` (str): Agent 描述，默认为 "An agent that uses an inner team of agents to generate responses."
- `instruction` (str): 指令
- `response_prompt` (str): 响应提示

**使用示例**:
```python
from agents.generic_agent import create_society_of_mind_agent, create_assistant_agent

# 创建内部团队成员
team_member = create_assistant_agent(
    model_alias='gpt-4',
    name='TeamMember'
)

agent = create_society_of_mind_agent(
    model_alias='gpt-4',
    name='SocietyOfMind',
    team=team_member,
    description='Multi-agent collaboration system',
    instruction='Coordinate with team members to solve complex tasks'
)
```

## Agent 管理器集成

### 直接创建 Agent

```python
from managers.agent_manager import get_agent_manager

agent_manager = get_agent_manager()

# 直接创建不同类型的 Agent
assistant = agent_manager.create_autogen_agent(
    agent_class='AssistantAgent',
    model_alias='gpt-4',
    name='MyAssistant',
    description='A helpful assistant'
)

user_proxy = agent_manager.create_autogen_agent(
    agent_class='UserProxyAgent',
    model_alias='gpt-4',
    name='UserProxy'
)
```

### 通过工厂函数创建

```python
# 注册通用 Agent 工厂
from agents.generic_agent import (
    create_assistant_agent,
    create_user_proxy_agent,
    create_code_executor_agent,
    create_society_of_mind_agent
)

agent_manager.register_agent_factory('generic_assistant', create_assistant_agent)
agent_manager.register_agent_factory('generic_user_proxy', create_user_proxy_agent)

# 通过工厂创建
agent = agent_manager.create_agent(
    agent_type='generic_assistant',
    model_alias='gpt-4',
    name='MyAgent'
)
```

## 最佳实践

### 1. 参数传递策略

- **常用参数**: 显式传递，提供清晰的接口
- **不常用参数**: 隐式传递，保持接口简洁
- **必需参数**: 明确标识并提供默认值或错误提示

### 2. 错误处理

- 为必需参数提供清晰的错误消息
- 支持模拟模式用于测试
- 提供默认值以简化使用

### 3. 提示词管理

- 为每种 Agent 类型创建专门的提示词模板
- 支持参数化提示词以适应不同场景
- 使用 Jinja2 模板引擎支持复杂逻辑

### 4. 模型客户端管理

- 统一通过模型管理器获取客户端
- 支持多种模型配置和别名
- 自动处理模型客户端的生命周期

## 扩展支持

### 添加新的 Agent 类型

1. 在 `generic_agent.py` 中添加新的创建函数
2. 参考官方文档定义参数
3. 实现常用参数显式传递，不常用参数隐式传递
4. 添加相应的提示词模板
5. 更新测试用例

### 自定义 Agent 工厂

```python
def create_custom_agent(
    model_alias: str,
    name: Optional[str] = None,
    custom_param: str = "default",
    **kwargs
) -> Any:
    """创建自定义 Agent"""
    # 实现自定义逻辑
    pass

# 注册到 Agent 管理器
agent_manager.register_agent_factory('custom', create_custom_agent)
```

## 技术细节

- **AutoGen 版本**: 0.6.2
- **支持的 Agent 基类**: BaseChatAgent
- **模型客户端**: ChatCompletionClient
- **工具系统**: BaseTool
- **内存系统**: Memory
- **日志系统**: AutoGen trace logger

## 参考资料

- [AutoGen 官方文档 - Agents](https://microsoft.github.io/autogen/stable/reference/python/autogen_agentchat.agents.html)
- [AutoGen 用户指南 - AgentChat](https://microsoft.github.io/autogen/stable/user-guide/agentchat-user-guide/index.html)
- [AutoGen 团队教程](https://microsoft.github.io/autogen/stable/user-guide/agentchat-user-guide/tutorial/teams.html)
