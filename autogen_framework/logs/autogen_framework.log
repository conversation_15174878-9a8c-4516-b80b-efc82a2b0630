2025-07-01 21:23:37 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 21:23:37 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 21:23:37 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 21:23:37 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 21:32:41 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 21:32:41 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 21:32:41 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 21:32:41 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 21:32:41 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 21:32:41 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 21:32:41 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 21:32:41 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 21:32:41 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 21:32:41 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 21:32:41 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 21:32:41 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 21:32:41 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 21:32:41 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 21:32:41 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 21:32:41 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 21:32:41 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 21:47:13 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 21:47:13 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 21:47:13 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 21:47:13 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 21:47:13 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 21:47:13 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 21:47:13 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 21:47:13 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 21:47:13 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 21:48:18 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 21:48:18 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 21:48:19 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 21:48:19 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 21:48:19 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 22:22:13 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 22:22:13 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 22:22:13 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 22:22:13 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 22:22:46 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 22:22:46 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 22:22:46 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 22:22:46 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 22:22:46 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 23:05:48 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:05:48 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:05:48 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:05:48 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:05:48 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 23:07:57 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:07:57 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:07:57 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:07:57 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:07:57 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 23:10:32 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:10:32 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:10:32 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:10:32 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:10:32 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 23:10:54 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:12:06 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:12:06 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 23:12:17 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:12:17 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 23:22:34 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:22:34 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 23:23:22 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:23:22 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:23:22 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:23:22 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:23:22 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 23:23:22 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 23:23:54 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:23:54 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:23:54 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:23:54 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:23:54 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 23:23:54 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 23:31:26 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:31:26 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 23:32:14 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:32:14 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-01 23:32:15 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-01 23:49:51 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:49:51 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:49:51 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-01 23:49:51 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 00:06:17 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 00:06:17 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 00:06:19 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 00:06:19 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 00:06:19 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 00:06:19 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 00:06:19 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 00:06:19 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 00:06:19 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 00:06:19 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 00:06:19 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 00:06:19 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 00:06:19 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 00:06:19 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 00:06:19 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 00:20:01 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 00:20:01 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 00:20:04 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 00:21:06 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 00:21:06 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 00:21:06 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 00:21:06 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 00:21:06 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 00:21:07 - autogen_core - INFO - Sending message of type GroupChatStart to RoundRobinGroupChatManager_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab: {'messages': [TextMessage(id='06c17518-4400-42c2-83af-2b5d753c802f', source='user', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 1, 16, 21, 7, 11032, tzinfo=datetime.timezone.utc), content='Hello team, this is a test message', type='TextMessage')], 'output_task_messages': True}
2025-07-02 00:21:07 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab/9eafbf60-97ec-49d6-b41b-fbcaa8e773ab with message type GroupChatStart sent by Unknown
2025-07-02 00:21:07 - autogen_core - INFO - Publishing message of type GroupChatStart to all subscribers: {'messages': [TextMessage(id='06c17518-4400-42c2-83af-2b5d753c802f', source='user', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 1, 16, 21, 7, 11032, tzinfo=datetime.timezone.utc), content='Hello team, this is a test message', type='TextMessage')], 'output_task_messages': True}
2025-07-02 00:21:07 - autogen_core - INFO - Publishing message of type GroupChatStart to all subscribers: {'messages': [TextMessage(id='06c17518-4400-42c2-83af-2b5d753c802f', source='user', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 1, 16, 21, 7, 11032, tzinfo=datetime.timezone.utc), content='Hello team, this is a test message', type='TextMessage')], 'output_task_messages': True}
2025-07-02 00:21:07 - autogen_core - INFO - Calling message handler for AdvancedDeveloper_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab with message type GroupChatStart published by RoundRobinGroupChatManager_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab/9eafbf60-97ec-49d6-b41b-fbcaa8e773ab
2025-07-02 00:21:07 - autogen_core - INFO - Calling message handler for SeniorReviewer_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab with message type GroupChatStart published by RoundRobinGroupChatManager_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab/9eafbf60-97ec-49d6-b41b-fbcaa8e773ab
2025-07-02 00:21:07 - autogen_core - INFO - Calling message handler for FullstackExpert_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab with message type GroupChatStart published by RoundRobinGroupChatManager_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab/9eafbf60-97ec-49d6-b41b-fbcaa8e773ab
2025-07-02 00:21:07 - autogen_core - INFO - Publishing message of type GroupChatRequestPublish to all subscribers: {}
2025-07-02 00:21:07 - autogen_core - INFO - Calling message handler for AdvancedDeveloper_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab with message type GroupChatRequestPublish published by RoundRobinGroupChatManager_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab/9eafbf60-97ec-49d6-b41b-fbcaa8e773ab
2025-07-02 00:21:07 - autogen_core - INFO - Resolving response with message type NoneType for recipient None from RoundRobinGroupChatManager_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab: None
2025-07-02 00:21:31 - autogen_core - INFO - Publishing message of type GroupChatMessage to all subscribers: {'message': TextMessage(id='5c782770-3c3e-4dd4-ad2e-3bd49bd5107e', source='AdvancedDeveloper', models_usage=RequestUsage(prompt_tokens=788, completion_tokens=749), metadata={}, created_at=datetime.datetime(2025, 7, 1, 16, 21, 31, 461700, tzinfo=datetime.timezone.utc), content='```python\n"""\n消息处理模块\n\n该模块提供消息接收、解析和响应功能，支持多种消息类型处理。\n"""\n\nimport json\nfrom typing import Dict, Any\n\nclass MessageProcessor:\n    """消息处理器\n    \n    负责接收原始消息数据，进行格式验证和业务逻辑处理\n    \n    Attributes:\n        message (str): 原始消息内容\n        parsed_data (Dict): 解析后的消息字典\n    """\n    \n    def __init__(self, message: str):\n        """初始化消息处理器\n        \n        Args:\n            message: 原始消息字符串，应为JSON格式\n            \n        Raises:\n            ValueError: 如果消息格式不正确\n        """\n        self.message = message\n        self.parsed_data = self._parse_message()\n        \n    def _parse_message(self) -> Dict[str, Any]:\n        """解析消息内容\n        \n        Returns:\n            包含消息数据的字典\n            \n        Raises:\n            ValueError: 当消息无法解析时抛出\n        """\n        try:\n            return json.loads(self.message)\n        except json.JSONDecodeError as e:\n            raise ValueError(f"Invalid message format: {str(e)}") from e\n\n    def process_message(self) -> Dict[str, Any]:\n        """处理消息并返回响应\n        \n        Returns:\n            处理结果字典，包含状态和响应信息\n        """\n        # 示例业务逻辑：检查消息中的特定字段\n        if not isinstance(self.parsed_data.get("content"), str):\n            return {\n                "status": "error",\n                "message": "Missing content field"\n            }\n            \n        return {\n            "status": "success",\n            "received_content": self.parsed_data["content"],\n            "processed_at": "2025-07-01T12:34:56Z"\n        }\n\n# 使用示例\nif __name__ == "__main__":\n    test_message = \'{"content": "Hello team, this is a test message"}\'\n    \n    processor = MessageProcessor(test_message)\n    response = processor.process_message()\n    \n    print(json.dumps(response, indent=2))\n```\n\n### 说明文档\n\n#### 设计思路\n1. **模块化设计**：将消息处理分为解析和业务处理两个阶段\n2. **类型安全**：使用Python类型注解确保数据结构正确性\n3. **错误处理**：通过异常机制处理无效输入\n4. **可扩展性**：通过继承或组合方式可轻松扩展新消息类型\n\n#### 关键实现\n1. **消息解析**：使用`json`模块进行结构化数据解析\n2. **验证机制**：检查必要字段的存在性和类型正确性\n3. **响应格式**：统一返回包含状态和元数据的标准化响应\n\n#### 使用示例\n```python\n# 创建处理器实例\nprocessor = MessageProcessor(\'{"content": "Test message"}\')\n\n# 处理消息并获取响应\nresponse = processor.process_message()\n\n# 输出示例：\n# {\n#   "status": "success",\n#   "received_content": "Test message",\n#   "processed_at": "2025-07-01T12:34:56Z"\n# }\n```\n\n#### 注意事项\n1. **消息格式要求**：输入必须为有效的JSON格式\n2. **字段验证**：确保包含`content`字段且为字符串类型\n3. **时区处理**：时间戳使用UTC时区格式\n4. **性能考虑**：对于高频场景建议添加异步处理机制', type='TextMessage')}
2025-07-02 00:21:31 - autogen_core - INFO - Publishing message of type GroupChatAgentResponse to all subscribers: {'agent_response': Response(chat_message=TextMessage(id='5c782770-3c3e-4dd4-ad2e-3bd49bd5107e', source='AdvancedDeveloper', models_usage=RequestUsage(prompt_tokens=788, completion_tokens=749), metadata={}, created_at=datetime.datetime(2025, 7, 1, 16, 21, 31, 461700, tzinfo=datetime.timezone.utc), content='```python\n"""\n消息处理模块\n\n该模块提供消息接收、解析和响应功能，支持多种消息类型处理。\n"""\n\nimport json\nfrom typing import Dict, Any\n\nclass MessageProcessor:\n    """消息处理器\n    \n    负责接收原始消息数据，进行格式验证和业务逻辑处理\n    \n    Attributes:\n        message (str): 原始消息内容\n        parsed_data (Dict): 解析后的消息字典\n    """\n    \n    def __init__(self, message: str):\n        """初始化消息处理器\n        \n        Args:\n            message: 原始消息字符串，应为JSON格式\n            \n        Raises:\n            ValueError: 如果消息格式不正确\n        """\n        self.message = message\n        self.parsed_data = self._parse_message()\n        \n    def _parse_message(self) -> Dict[str, Any]:\n        """解析消息内容\n        \n        Returns:\n            包含消息数据的字典\n            \n        Raises:\n            ValueError: 当消息无法解析时抛出\n        """\n        try:\n            return json.loads(self.message)\n        except json.JSONDecodeError as e:\n            raise ValueError(f"Invalid message format: {str(e)}") from e\n\n    def process_message(self) -> Dict[str, Any]:\n        """处理消息并返回响应\n        \n        Returns:\n            处理结果字典，包含状态和响应信息\n        """\n        # 示例业务逻辑：检查消息中的特定字段\n        if not isinstance(self.parsed_data.get("content"), str):\n            return {\n                "status": "error",\n                "message": "Missing content field"\n            }\n            \n        return {\n            "status": "success",\n            "received_content": self.parsed_data["content"],\n            "processed_at": "2025-07-01T12:34:56Z"\n        }\n\n# 使用示例\nif __name__ == "__main__":\n    test_message = \'{"content": "Hello team, this is a test message"}\'\n    \n    processor = MessageProcessor(test_message)\n    response = processor.process_message()\n    \n    print(json.dumps(response, indent=2))\n```\n\n### 说明文档\n\n#### 设计思路\n1. **模块化设计**：将消息处理分为解析和业务处理两个阶段\n2. **类型安全**：使用Python类型注解确保数据结构正确性\n3. **错误处理**：通过异常机制处理无效输入\n4. **可扩展性**：通过继承或组合方式可轻松扩展新消息类型\n\n#### 关键实现\n1. **消息解析**：使用`json`模块进行结构化数据解析\n2. **验证机制**：检查必要字段的存在性和类型正确性\n3. **响应格式**：统一返回包含状态和元数据的标准化响应\n\n#### 使用示例\n```python\n# 创建处理器实例\nprocessor = MessageProcessor(\'{"content": "Test message"}\')\n\n# 处理消息并获取响应\nresponse = processor.process_message()\n\n# 输出示例：\n# {\n#   "status": "success",\n#   "received_content": "Test message",\n#   "processed_at": "2025-07-01T12:34:56Z"\n# }\n```\n\n#### 注意事项\n1. **消息格式要求**：输入必须为有效的JSON格式\n2. **字段验证**：确保包含`content`字段且为字符串类型\n3. **时区处理**：时间戳使用UTC时区格式\n4. **性能考虑**：对于高频场景建议添加异步处理机制', type='TextMessage'), inner_messages=[]), 'agent_name': 'AdvancedDeveloper'}
2025-07-02 00:21:31 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab with message type GroupChatMessage published by AdvancedDeveloper_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab/9eafbf60-97ec-49d6-b41b-fbcaa8e773ab
2025-07-02 00:21:31 - autogen_core - INFO - Calling message handler for SeniorReviewer_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab with message type GroupChatAgentResponse published by AdvancedDeveloper_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab/9eafbf60-97ec-49d6-b41b-fbcaa8e773ab
2025-07-02 00:21:31 - autogen_core - INFO - Calling message handler for FullstackExpert_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab with message type GroupChatAgentResponse published by AdvancedDeveloper_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab/9eafbf60-97ec-49d6-b41b-fbcaa8e773ab
2025-07-02 00:21:31 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab with message type GroupChatAgentResponse published by AdvancedDeveloper_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab/9eafbf60-97ec-49d6-b41b-fbcaa8e773ab
2025-07-02 00:21:31 - autogen_core - INFO - Publishing message of type GroupChatRequestPublish to all subscribers: {}
2025-07-02 00:21:31 - autogen_core - INFO - Calling message handler for SeniorReviewer_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab with message type GroupChatRequestPublish published by RoundRobinGroupChatManager_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab/9eafbf60-97ec-49d6-b41b-fbcaa8e773ab
2025-07-02 00:22:05 - autogen_core - INFO - Publishing message of type GroupChatMessage to all subscribers: {'message': TextMessage(id='f4ead8ae-9f67-4819-aff2-eca41ba41e58', source='SeniorReviewer', models_usage=RequestUsage(prompt_tokens=2069, completion_tokens=870), metadata={}, created_at=datetime.datetime(2025, 7, 1, 16, 22, 5, 526153, tzinfo=datetime.timezone.utc), content='# 代码审查执行摘要\n\n## 整体评估\n- **架构质量**: 良好\n- **技术债务**: 中\n- **安全等级**: 一般\n- **性能评级**: 良好\n\n## 关键发现\n- **架构问题**: 硬编码时间戳导致测试困难和数据不一致风险\n- **性能问题**: 同步处理可能成为高频场景瓶颈\n- **安全风险**: 缺少输入内容长度校验，存在DoS攻击风险\n\n## 优先级建议\n1. 实现动态时间戳生成\n2. 添加消息内容长度限制\n3. 增加异步处理支持\n4. 完善单元测试覆盖\n\n### 详细分析报告\n\n#### 架构分析\n```\n## 架构设计分析\n\n### 优点\n- 清晰的职责分离（解析与处理分离）\n- 类型注解提升代码可维护性\n- 统一的错误处理机制\n\n### 问题\n- **硬编码时间戳**：`processed_at`使用固定值而非动态生成\n- **扩展性不足**：业务逻辑直接内聚在process_message方法中\n- **缺乏接口定义**：未明确消息处理接口规范\n\n### 改进方案\n1. 使用`datetime.utcnow()`生成动态时间戳\n2. 将业务逻辑拆分为独立方法或策略模式\n3. 定义消息处理接口规范\n```\n\n#### 性能分析\n```\n## 性能优化分析\n\n### 性能瓶颈\n- **同步处理限制**：当前实现无法有效处理高并发场景\n- **JSON解析开销**：未考虑高性能JSON库替代\n\n### 扩展性评估\n- 水平扩展能力受限于同步处理模式\n- 建议添加消息队列实现异步处理\n```\n\n#### 安全分析\n```\n## 安全性分析\n\n### 安全风险\n- **DoS风险**：缺少消息内容长度校验\n- **注入风险**：未对content字段进行特殊字符过滤\n- **认证缺失**：未包含消息来源验证机制\n\n### 防护建议\n1. 添加内容长度校验（如最大5000字符）\n2. 对输出内容进行HTML转义\n3. 增加消息签名验证机制\n```\n\n## 专家建议\n\n### 重构建议\n```python\nfrom datetime import datetime, timezone\n\nclass MessageProcessor:\n    # ... 其他保持不变 ...\n\n    def process_message(self) -> Dict[str, Any]:\n        if not isinstance(self.parsed_data.get("content"), str):\n            return self._error_response("Missing content field")\n            \n        if len(self.parsed_data["content"]) > 5000:\n            return self._error_response("Content too long")\n\n        return {\n            "status": "success",\n            "received_content": self._sanitize_content(self.parsed_data["content"]),\n            "processed_at": datetime.now(timezone.utc).isoformat()\n        }\n\n    def _error_response(self, message: str) -> Dict:\n        return {"status": "error", "message": message}\n\n    def _sanitize_content(self, content: str) -> str:\n        return content.replace("&", "&amp;").replace("<", "&lt;")\n```\n\n### 技术演进\n1. **异步处理**：集成asyncio实现非阻塞IO\n2. **监控指标**：添加处理耗时和成功率监控\n3. **内容过滤**：集成第三方安全库进行深度内容校验\n\n### 工具链优化\n- 添加pytest-benchmark进行性能基准测试\n- 集成bandit进行安全静态分析\n- 使用mypy增强类型检查\n\n## 特殊审查要求响应\n1. **架构改进**：通过策略模式实现多消息类型处理\n2. **性能优化**：建议使用ujson替代标准json库\n3. **安全加固**：增加内容长度校验和XSS防护\n4. **可维护性**：完善单元测试覆盖边界条件', type='TextMessage')}
2025-07-02 00:22:05 - autogen_core - INFO - Publishing message of type GroupChatAgentResponse to all subscribers: {'agent_response': Response(chat_message=TextMessage(id='f4ead8ae-9f67-4819-aff2-eca41ba41e58', source='SeniorReviewer', models_usage=RequestUsage(prompt_tokens=2069, completion_tokens=870), metadata={}, created_at=datetime.datetime(2025, 7, 1, 16, 22, 5, 526153, tzinfo=datetime.timezone.utc), content='# 代码审查执行摘要\n\n## 整体评估\n- **架构质量**: 良好\n- **技术债务**: 中\n- **安全等级**: 一般\n- **性能评级**: 良好\n\n## 关键发现\n- **架构问题**: 硬编码时间戳导致测试困难和数据不一致风险\n- **性能问题**: 同步处理可能成为高频场景瓶颈\n- **安全风险**: 缺少输入内容长度校验，存在DoS攻击风险\n\n## 优先级建议\n1. 实现动态时间戳生成\n2. 添加消息内容长度限制\n3. 增加异步处理支持\n4. 完善单元测试覆盖\n\n### 详细分析报告\n\n#### 架构分析\n```\n## 架构设计分析\n\n### 优点\n- 清晰的职责分离（解析与处理分离）\n- 类型注解提升代码可维护性\n- 统一的错误处理机制\n\n### 问题\n- **硬编码时间戳**：`processed_at`使用固定值而非动态生成\n- **扩展性不足**：业务逻辑直接内聚在process_message方法中\n- **缺乏接口定义**：未明确消息处理接口规范\n\n### 改进方案\n1. 使用`datetime.utcnow()`生成动态时间戳\n2. 将业务逻辑拆分为独立方法或策略模式\n3. 定义消息处理接口规范\n```\n\n#### 性能分析\n```\n## 性能优化分析\n\n### 性能瓶颈\n- **同步处理限制**：当前实现无法有效处理高并发场景\n- **JSON解析开销**：未考虑高性能JSON库替代\n\n### 扩展性评估\n- 水平扩展能力受限于同步处理模式\n- 建议添加消息队列实现异步处理\n```\n\n#### 安全分析\n```\n## 安全性分析\n\n### 安全风险\n- **DoS风险**：缺少消息内容长度校验\n- **注入风险**：未对content字段进行特殊字符过滤\n- **认证缺失**：未包含消息来源验证机制\n\n### 防护建议\n1. 添加内容长度校验（如最大5000字符）\n2. 对输出内容进行HTML转义\n3. 增加消息签名验证机制\n```\n\n## 专家建议\n\n### 重构建议\n```python\nfrom datetime import datetime, timezone\n\nclass MessageProcessor:\n    # ... 其他保持不变 ...\n\n    def process_message(self) -> Dict[str, Any]:\n        if not isinstance(self.parsed_data.get("content"), str):\n            return self._error_response("Missing content field")\n            \n        if len(self.parsed_data["content"]) > 5000:\n            return self._error_response("Content too long")\n\n        return {\n            "status": "success",\n            "received_content": self._sanitize_content(self.parsed_data["content"]),\n            "processed_at": datetime.now(timezone.utc).isoformat()\n        }\n\n    def _error_response(self, message: str) -> Dict:\n        return {"status": "error", "message": message}\n\n    def _sanitize_content(self, content: str) -> str:\n        return content.replace("&", "&amp;").replace("<", "&lt;")\n```\n\n### 技术演进\n1. **异步处理**：集成asyncio实现非阻塞IO\n2. **监控指标**：添加处理耗时和成功率监控\n3. **内容过滤**：集成第三方安全库进行深度内容校验\n\n### 工具链优化\n- 添加pytest-benchmark进行性能基准测试\n- 集成bandit进行安全静态分析\n- 使用mypy增强类型检查\n\n## 特殊审查要求响应\n1. **架构改进**：通过策略模式实现多消息类型处理\n2. **性能优化**：建议使用ujson替代标准json库\n3. **安全加固**：增加内容长度校验和XSS防护\n4. **可维护性**：完善单元测试覆盖边界条件', type='TextMessage'), inner_messages=[]), 'agent_name': 'SeniorReviewer'}
2025-07-02 00:22:05 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab with message type GroupChatMessage published by SeniorReviewer_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab/9eafbf60-97ec-49d6-b41b-fbcaa8e773ab
2025-07-02 00:22:05 - autogen_core - INFO - Calling message handler for AdvancedDeveloper_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab with message type GroupChatAgentResponse published by SeniorReviewer_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab/9eafbf60-97ec-49d6-b41b-fbcaa8e773ab
2025-07-02 00:22:05 - autogen_core - INFO - Calling message handler for FullstackExpert_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab with message type GroupChatAgentResponse published by SeniorReviewer_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab/9eafbf60-97ec-49d6-b41b-fbcaa8e773ab
2025-07-02 00:22:05 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab with message type GroupChatAgentResponse published by SeniorReviewer_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab/9eafbf60-97ec-49d6-b41b-fbcaa8e773ab
2025-07-02 00:22:05 - autogen_core - INFO - Publishing message of type GroupChatRequestPublish to all subscribers: {}
2025-07-02 00:22:05 - autogen_core - INFO - Calling message handler for FullstackExpert_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab with message type GroupChatRequestPublish published by RoundRobinGroupChatManager_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab/9eafbf60-97ec-49d6-b41b-fbcaa8e773ab
2025-07-02 00:22:05 - autogen_core - INFO - Publishing message of type GroupChatError to all subscribers: {'error': SerializableException(error_type='TypeError', error_message="'NoneType' object is not subscriptable", traceback='Traceback (most recent call last):\n\n  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_agentchat/teams/_group_chat/_chat_agent_container.py", line 84, in handle_request\n    async for msg in self._agent.on_messages_stream(self._message_buffer, ctx.cancellation_token):\n    ...<4 lines>...\n            await self._log_message(msg)\n\n  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 964, in on_messages_stream\n    async for inference_output in self._call_llm(\n    ...<15 lines>...\n            yield inference_output\n\n  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 1118, in _call_llm\n    model_result = await model_client.create(\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<4 lines>...\n    )\n    ^\n\n  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_ext/models/openai/_openai_client.py", line 708, in create\n    choice: Union[ParsedChoice[Any], ParsedChoice[BaseModel], Choice] = result.choices[0]\n                                                                        ~~~~~~~~~~~~~~^^^\n\nTypeError: \'NoneType\' object is not subscriptable\n')}
2025-07-02 00:22:05 - autogen_core - ERROR - Error processing publish message for FullstackExpert_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab/9eafbf60-97ec-49d6-b41b-fbcaa8e773ab
Traceback (most recent call last):
  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_core/_single_threaded_agent_runtime.py", line 606, in _on_message
    return await agent.on_message(
           ^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )
    ^
  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_core/_base_agent.py", line 119, in on_message
    return await self.on_message_impl(message, ctx)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_agentchat/teams/_group_chat/_sequential_routed_agent.py", line 67, in on_message_impl
    return await super().on_message_impl(message, ctx)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_core/_routed_agent.py", line 485, in on_message_impl
    return await h(self, message, ctx)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_core/_routed_agent.py", line 268, in wrapper
    return_value = await func(self, message, ctx)  # type: ignore
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_agentchat/teams/_group_chat/_chat_agent_container.py", line 84, in handle_request
    async for msg in self._agent.on_messages_stream(self._message_buffer, ctx.cancellation_token):
    ...<4 lines>...
            await self._log_message(msg)
  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 964, in on_messages_stream
    async for inference_output in self._call_llm(
    ...<15 lines>...
            yield inference_output
  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 1118, in _call_llm
    model_result = await model_client.create(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_ext/models/openai/_openai_client.py", line 708, in create
    choice: Union[ParsedChoice[Any], ParsedChoice[BaseModel], Choice] = result.choices[0]
                                                                        ~~~~~~~~~~~~~~^^^
TypeError: 'NoneType' object is not subscriptable
2025-07-02 00:22:05 - autogen_core - INFO - Calling message handler for AdvancedDeveloper_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab with message type GroupChatError published by FullstackExpert_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab/9eafbf60-97ec-49d6-b41b-fbcaa8e773ab
2025-07-02 00:22:05 - autogen_core - INFO - Calling message handler for SeniorReviewer_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab with message type GroupChatError published by FullstackExpert_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab/9eafbf60-97ec-49d6-b41b-fbcaa8e773ab
2025-07-02 00:22:05 - autogen_core - INFO - Calling message handler for RoundRobinGroupChatManager_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab with message type GroupChatError published by FullstackExpert_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab/9eafbf60-97ec-49d6-b41b-fbcaa8e773ab
2025-07-02 00:22:05 - autogen_core - ERROR - Error processing publish message for AdvancedDeveloper_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab/9eafbf60-97ec-49d6-b41b-fbcaa8e773ab
Traceback (most recent call last):
  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_core/_single_threaded_agent_runtime.py", line 606, in _on_message
    return await agent.on_message(
           ^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )
    ^
  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_core/_base_agent.py", line 119, in on_message
    return await self.on_message_impl(message, ctx)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_agentchat/teams/_group_chat/_sequential_routed_agent.py", line 72, in on_message_impl
    return await super().on_message_impl(message, ctx)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_core/_routed_agent.py", line 486, in on_message_impl
    return await self.on_unhandled_message(message, ctx)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_agentchat/teams/_group_chat/_chat_agent_container.py", line 138, in on_unhandled_message
    raise ValueError(f"Unhandled message in agent container: {type(message)}")
ValueError: Unhandled message in agent container: <class 'autogen_agentchat.teams._group_chat._events.GroupChatError'>
2025-07-02 00:22:05 - autogen_core - ERROR - Error processing publish message for SeniorReviewer_9eafbf60-97ec-49d6-b41b-fbcaa8e773ab/9eafbf60-97ec-49d6-b41b-fbcaa8e773ab
Traceback (most recent call last):
  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_core/_single_threaded_agent_runtime.py", line 606, in _on_message
    return await agent.on_message(
           ^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )
    ^
  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_core/_base_agent.py", line 119, in on_message
    return await self.on_message_impl(message, ctx)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_agentchat/teams/_group_chat/_sequential_routed_agent.py", line 72, in on_message_impl
    return await super().on_message_impl(message, ctx)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_core/_routed_agent.py", line 486, in on_message_impl
    return await self.on_unhandled_message(message, ctx)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_agentchat/teams/_group_chat/_chat_agent_container.py", line 138, in on_unhandled_message
    raise ValueError(f"Unhandled message in agent container: {type(message)}")
ValueError: Unhandled message in agent container: <class 'autogen_agentchat.teams._group_chat._events.GroupChatError'>
2025-07-02 00:22:05 - autogen_core - INFO - Publishing message of type GroupChatTermination to all subscribers: {'message': StopMessage(id='e500a63b-e41e-4282-8d1c-33746233b34b', source='RoundRobinGroupChatManager', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 7, 1, 16, 22, 5, 971725, tzinfo=datetime.timezone.utc), content='An error occurred in the group chat.', type='StopMessage'), 'error': SerializableException(error_type='TypeError', error_message="'NoneType' object is not subscriptable", traceback='Traceback (most recent call last):\n\n  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_agentchat/teams/_group_chat/_chat_agent_container.py", line 84, in handle_request\n    async for msg in self._agent.on_messages_stream(self._message_buffer, ctx.cancellation_token):\n    ...<4 lines>...\n            await self._log_message(msg)\n\n  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 964, in on_messages_stream\n    async for inference_output in self._call_llm(\n    ...<15 lines>...\n            yield inference_output\n\n  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 1118, in _call_llm\n    model_result = await model_client.create(\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<4 lines>...\n    )\n    ^\n\n  File "/home/<USER>/Desktop/autogen/venv/lib/python3.13/site-packages/autogen_ext/models/openai/_openai_client.py", line 708, in create\n    choice: Union[ParsedChoice[Any], ParsedChoice[BaseModel], Choice] = result.choices[0]\n                                                                        ~~~~~~~~~~~~~~^^^\n\nTypeError: \'NoneType\' object is not subscriptable\n')}
2025-07-02 01:19:14 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:19:14 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:19:14 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:20:37 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:20:37 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:20:37 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:21:23 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:21:23 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:21:23 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:22:17 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:22:17 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 01:22:17 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:22:17 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:22:17 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 01:24:07 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 01:24:07 - autogen_framework.main - INFO - Environment: development
2025-07-02 01:24:07 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 01:24:07 - autogen_framework.main - INFO - Application startup complete
2025-07-02 01:26:27 - autogen_framework.main - INFO - Shutting down application
2025-07-02 01:26:27 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 01:26:55 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 01:26:55 - autogen_framework.main - INFO - Environment: development
2025-07-02 01:26:55 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 01:26:55 - autogen_framework.main - INFO - Application startup complete
2025-07-02 01:27:39 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:27:39 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:27:39 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:27:39 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 01:27:41 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 01:31:05 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 01:31:05 - autogen_framework.main - INFO - Environment: development
2025-07-02 01:31:05 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 01:31:05 - autogen_framework.main - INFO - Application startup complete
2025-07-02 01:31:45 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:33:16 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:33:16 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:35:44 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 01:36:20 - autogen_framework.main - INFO - Shutting down application
2025-07-02 01:36:20 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 01:36:22 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 01:36:22 - autogen_framework.main - INFO - Environment: development
2025-07-02 01:36:22 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 01:36:22 - autogen_framework.main - INFO - Application startup complete
2025-07-02 01:36:32 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:36:32 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 01:37:00 - autogen_framework.main - INFO - Shutting down application
2025-07-02 01:37:00 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 01:37:05 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 01:37:05 - autogen_framework.main - INFO - Environment: development
2025-07-02 01:37:05 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 01:37:05 - autogen_framework.main - INFO - Application startup complete
2025-07-02 01:37:12 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 01:37:12 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 01:37:13 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:29:29 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:29:29 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:29:34 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:29:34 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:29:34 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:29:34 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:29:49 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:29:49 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:29:51 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:29:51 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:29:51 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:29:51 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:30:37 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:30:37 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:30:39 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:30:39 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:30:39 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:30:39 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:31:40 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:31:40 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:31:42 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:31:42 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:31:42 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:31:42 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:33:00 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:33:00 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:33:02 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:33:02 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:33:02 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:33:02 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:33:15 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:33:15 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:33:18 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:33:18 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:33:18 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:33:18 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:33:18 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:33:18 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:33:18 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:33:18 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:33:18 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:33:18 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:33:18 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:33:18 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:33:18 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:33:46 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:33:46 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:33:49 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:33:49 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:33:49 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:33:49 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:34:18 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:34:18 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:34:20 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:34:20 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:34:20 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:34:20 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:34:40 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:34:40 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:34:42 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:34:42 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:34:42 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:34:42 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:34:57 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:34:57 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:34:59 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:34:59 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:34:59 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:34:59 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:35:24 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:35:24 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:35:26 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:35:26 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:35:26 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:35:26 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:35:37 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:35:37 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:35:39 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:35:39 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:35:39 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:35:39 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:35:39 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:35:39 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:35:39 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:35:39 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:35:39 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:35:39 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:35:39 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:35:39 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:35:39 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:35:39 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:35:39 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:35:39 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:35:39 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:35:39 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:35:39 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:36:42 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:36:42 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:36:45 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:36:45 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:36:45 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:36:45 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:36:54 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:36:54 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:36:56 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:36:56 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:36:56 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:36:56 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:36:56 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:36:56 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:36:56 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:36:56 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:36:56 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:36:56 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:36:56 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:36:56 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:36:56 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:36:56 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:36:56 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:36:56 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:36:56 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:36:56 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:36:56 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:47:08 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:47:08 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:47:10 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:47:10 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:47:10 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:47:10 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:47:41 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:47:41 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:47:43 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:47:43 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:47:43 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:47:43 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:48:14 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:48:14 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:48:17 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:48:17 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:48:17 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:48:17 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:49:03 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:49:03 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:49:05 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:49:05 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:49:05 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:49:05 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:49:34 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:49:34 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:49:36 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:49:36 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:49:36 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:49:36 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:50:24 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:50:24 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:50:26 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:50:26 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:50:26 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:50:26 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:50:36 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:50:36 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:50:37 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:50:37 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:50:37 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:50:37 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:50:37 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:50:38 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:50:38 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:50:38 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:50:38 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:50:38 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:50:38 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:50:38 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:50:38 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:50:38 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:50:38 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:50:38 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:50:38 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:50:38 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:50:38 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:50:38 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:50:38 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:50:38 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:51:52 - autogen_framework.main - INFO - Shutting down application
2025-07-02 02:51:52 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 02:51:54 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 02:51:54 - autogen_framework.main - INFO - Environment: development
2025-07-02 02:51:54 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 02:51:54 - autogen_framework.main - INFO - Application startup complete
2025-07-02 02:52:06 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:52:06 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:52:07 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:52:07 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:52:07 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:52:07 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:52:07 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:52:07 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:52:07 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:52:07 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:52:07 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:52:07 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:52:07 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:52:07 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:52:07 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:52:07 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:52:07 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:52:07 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:52:07 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:52:07 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 02:52:07 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:52:07 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 02:52:07 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 02:52:07 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 03:12:24 - autogen_framework.main - INFO - Shutting down application
2025-07-02 03:12:24 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 03:12:25 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 03:12:25 - autogen_framework.main - INFO - Environment: development
2025-07-02 03:12:25 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 03:12:25 - autogen_framework.main - INFO - Application startup complete
2025-07-02 03:13:01 - autogen_framework.main - INFO - Shutting down application
2025-07-02 03:13:01 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 03:13:03 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 03:13:03 - autogen_framework.main - INFO - Environment: development
2025-07-02 03:13:03 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 03:13:03 - autogen_framework.main - INFO - Application startup complete
2025-07-02 03:13:44 - autogen_framework.main - INFO - Shutting down application
2025-07-02 03:13:44 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 03:13:46 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 03:13:46 - autogen_framework.main - INFO - Environment: development
2025-07-02 03:13:46 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 03:13:46 - autogen_framework.main - INFO - Application startup complete
2025-07-02 03:14:10 - autogen_framework.main - INFO - Shutting down application
2025-07-02 03:14:10 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 03:14:11 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 03:14:11 - autogen_framework.main - INFO - Environment: development
2025-07-02 03:14:11 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 03:14:11 - autogen_framework.main - INFO - Application startup complete
2025-07-02 03:14:42 - autogen_framework.main - INFO - Shutting down application
2025-07-02 03:14:42 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 03:14:44 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 03:14:44 - autogen_framework.main - INFO - Environment: development
2025-07-02 03:14:44 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 03:14:44 - autogen_framework.main - INFO - Application startup complete
2025-07-02 03:15:10 - autogen_framework.main - INFO - Shutting down application
2025-07-02 03:15:10 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 03:15:12 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 03:15:12 - autogen_framework.main - INFO - Environment: development
2025-07-02 03:15:12 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 03:15:12 - autogen_framework.main - INFO - Application startup complete
2025-07-02 03:15:20 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 03:15:20 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 03:15:20 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 03:15:20 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 03:15:21 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 03:15:21 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 03:15:21 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 03:15:21 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 03:15:21 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 03:36:28 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 03:36:28 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 03:36:29 - autogen_framework.model_manager - INFO - Created model client for nebulacoder-v6.0
2025-07-02 09:52:13 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:52:13 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:52:16 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:52:16 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:52:16 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:52:16 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:52:26 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:52:26 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:52:29 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:52:29 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:52:29 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:52:29 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:52:41 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:52:41 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:52:43 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:52:43 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:52:43 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:52:43 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:52:55 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:52:55 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:52:57 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:52:57 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:52:57 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:52:57 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:53:15 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:53:15 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:53:17 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:53:17 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:53:17 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:53:17 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:53:47 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:53:47 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:53:49 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:53:49 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:53:49 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:53:49 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:53:59 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:53:59 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:54:01 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:54:01 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:54:01 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:54:01 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:54:25 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:54:25 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:54:27 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:54:27 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:54:27 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:54:27 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:54:40 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:54:40 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:54:41 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:54:41 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:54:41 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:54:41 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:55:00 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:55:00 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:55:02 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:55:02 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:55:02 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:55:02 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:55:16 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:55:16 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:55:18 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:55:18 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:55:18 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:55:18 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:55:32 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:55:32 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:55:34 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:55:34 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:55:34 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:55:34 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:55:46 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:55:46 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:55:48 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:55:48 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:55:48 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:55:48 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:56:03 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:56:03 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:56:05 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:56:05 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:56:05 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:56:05 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:56:20 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:56:20 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:56:22 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:56:22 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:56:22 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:56:22 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:56:57 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:56:57 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:56:59 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:56:59 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:56:59 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:56:59 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:57:14 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 09:57:14 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 09:57:14 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 09:57:14 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 09:57:14 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 09:57:14 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 09:57:35 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:57:35 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:57:37 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:57:37 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:57:37 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:57:37 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:57:58 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:57:58 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:58:00 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:58:00 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:58:00 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:58:00 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:58:10 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 09:58:10 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 09:58:10 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 09:58:10 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 09:58:10 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 09:58:10 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 09:59:25 - autogen_framework.main - INFO - Shutting down application
2025-07-02 09:59:25 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 09:59:28 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 09:59:28 - autogen_framework.main - INFO - Environment: development
2025-07-02 09:59:28 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 09:59:28 - autogen_framework.main - INFO - Application startup complete
2025-07-02 09:59:35 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 09:59:35 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 09:59:35 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 09:59:35 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 09:59:35 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 09:59:35 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 10:12:07 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:12:07 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:12:10 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:12:10 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:12:10 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:12:10 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:12:24 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:12:24 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:12:26 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:12:26 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:12:26 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:12:26 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:12:38 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:12:38 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:12:39 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:12:39 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:12:39 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:12:39 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:12:49 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:12:49 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:12:50 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:12:50 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:12:50 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:12:50 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:13:09 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:13:09 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:13:11 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:13:11 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:13:11 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:13:11 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:13:29 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:13:29 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:13:32 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:13:32 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:13:32 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:13:32 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:13:42 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:13:42 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:13:44 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:13:44 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:13:44 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:13:44 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:13:52 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:13:52 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:13:53 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:13:53 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:13:53 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:13:53 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:14:12 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:14:12 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:14:14 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:14:14 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:14:14 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:14:14 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:14:30 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:14:30 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:14:32 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:14:32 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:14:32 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:14:32 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:14:51 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:14:51 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:14:53 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:14:53 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:14:53 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:14:53 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:15:05 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:15:05 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:15:07 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:15:07 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:15:07 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:15:07 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:15:30 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:15:30 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:15:32 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:15:32 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:15:32 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:15:32 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:16:10 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:16:10 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:16:12 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:16:12 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:16:12 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:16:12 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:16:20 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 10:16:20 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 10:16:20 - autogen_framework.model_manager - INFO - Loaded 9 model configurations
2025-07-02 10:17:24 - autogen_framework.main - INFO - Shutting down application
2025-07-02 10:17:24 - autogen_framework.main - INFO - Application shutdown complete
2025-07-02 10:17:27 - autogen_framework.main - INFO - Starting AutoGen Multi-Agent Framework v1.0.0
2025-07-02 10:17:27 - autogen_framework.main - INFO - Environment: development
2025-07-02 10:17:27 - autogen_framework.main - INFO - Debug mode: True
2025-07-02 10:17:27 - autogen_framework.main - INFO - Application startup complete
2025-07-02 10:17:33 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 10:17:33 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 10:17:33 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
2025-07-02 10:17:33 - autogen_framework.model_manager - INFO - Model manager initialized with config: /home/<USER>/Desktop/autogen/autogen_framework/config/model_config.yaml
